"""
Pipeline service module for handling conversational AI.

NOTE TO LLM AGENTS: Do not mention any 'information being displayed' to the user 
during ID verification or other processes unless the information is explicitly shown 
in the WhatsApp interface. Users cannot see extracted information in the background.
"""
from typing import Dict, List, Any, Optional, Callable
import json
import logging
import openai
import time
import ast
from enum import Enum
from pydantic import BaseModel, Field
import os
import random
import re
import base64
import app.services.image_service as image_agent
import datetime
from openai import OpenAI, AzureOpenAI

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Gemini client setup for text using OpenAI-compatible API
# The OpenAI client with Gemini base URL provides compatibility with existing OpenAI-based code
# This approach allows using Gemini models with code originally written for OpenAI
client = OpenAI(
    api_key=os.getenv("GEMINI_API_KEY", "AIzaSyD69M4v4fQ0xYQRGHZXD6e9xE9XGWrr3Xw"),
    base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
)

# Image Analysis client for Azure OpenAI Vision
vision_client = AzureOpenAI(
    api_key="********************************",
    api_version="2024-02-01",
    azure_endpoint="https://adya-openai-aus.openai.azure.com/"
)

# Vision model name
vision_model = os.getenv("AZURE_VISION_MODEL", "gpt4vision_deployed")

# Set the models for text completion
MODEL = "gemini-2.0-flash"
FALLBACK_MODEL = "gemini-2.0-flash-lite"

def brute_force_json_extraction(text: str) -> dict:
    """
    A last-resort method to extract JSON from text when all other methods fail.
    This parses the JSON by manually extracting key parts.
    """
    logger.info("Using brute force JSON extraction as last resort")
    
    # Default result if all else fails
    result = {
        "response": "",
        "should_progress": "no",
        "state_field": {}
    }
    
    try:
        # If we have markdown code blocks, remove them first
        if "```" in text:
            # Extract from code blocks
            code_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
            code_block_match = re.search(code_block_pattern, text)
            if code_block_match:
                text = code_block_match.group(1).strip()
                logger.info(f"Brute force: removed markdown blocks: '{text[:30]}...'")
        
        # Try to extract a full JSON object first
        json_pattern = r'(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
        full_json_match = re.search(json_pattern, text)
        if full_json_match:
            potential_json = full_json_match.group(1)
            try:
                # Try to parse as complete JSON first
                parsed_json = json.loads(potential_json)
                logger.info("Brute force: Successfully parsed complete JSON object")
                
                # Check for required fields
                if "response" in parsed_json:
                    return parsed_json
            except json.JSONDecodeError:
                logger.info("Brute force: Found JSON-like structure but couldn't parse it directly")
        
        # New approach: Find the message text directly in cases where JSON is malformed
        # Check if the message content has raw JSON formatting that wasn't parsed correctly
        # This is for cases where the output shows fields directly in the message, like:
        # "Our digital process is secure and designed to save you time. Is there anything else I can help you with in the meantime?", "should_progress": "no", "state_field
        
        # Identify the main message content before any JSON fields appear
        malformed_pattern = r'^(.*?)(?:,\s*"should_progress"|,\s*"state_field")'
        malformed_match = re.search(malformed_pattern, text, re.DOTALL)
        if malformed_match:
            main_content = malformed_match.group(1).strip()
            # Clean up any trailing quotes or commas
            main_content = re.sub(r'",?\s*$', '', main_content)
            # Clean up any leading quotes
            main_content = re.sub(r'^"', '', main_content)
            
            result["response"] = main_content
            logger.info(f"Brute force: extracted main content before JSON fields: '{main_content[:30]}...'")
            
            # Now we need to reconstruct proper JSON from the malformed text
            reconstructed_json = "{"
            reconstructed_json += f'"response": "{main_content}"'
            
            # Add other fields if they're in the text
            for field in ["should_progress", "state_field"]:
                field_pattern = f',?\\s*"{field}"\\s*:\\s*(.+?)(?:,\\s*"[^"]+"|\\}})'
                field_match = re.search(field_pattern, text, re.DOTALL)
                if field_match:
                    field_value = field_match.group(1).strip()
                    reconstructed_json += f', "{field}": {field_value}'
            
            reconstructed_json += "}"
            
            try:
                parsed_reconstructed = json.loads(reconstructed_json)
                logger.info(f"Brute force: Successfully reconstructed and parsed JSON: {parsed_reconstructed}")
                return parsed_reconstructed
            except json.JSONDecodeError as e:
                logger.warning(f"Brute force: Failed to parse reconstructed JSON: {e}")
        
        # If above approach fails, continue with standard extraction methods
        # Extract response field
        response_pattern = r'"response"\s*:\s*"([^"]*(?:"[^"]*)*)"'
        response_match = re.search(response_pattern, text)
        if response_match:
            result["response"] = response_match.group(1)
            logger.info(f"Brute force: extracted response field: '{result['response'][:30]}...'")
        else:
            # Try alternate pattern for response with escaped quotes
            alt_response_pattern = r'"response"\s*:\s*"(.*?)(?<!\\)"(?=,|\s*\})'
            alt_response_match = re.search(alt_response_pattern, text, re.DOTALL)
            if alt_response_match:
                result["response"] = alt_response_match.group(1)
                logger.info(f"Brute force: extracted response field with alt pattern: '{result['response'][:30]}...'")
            else:
                # If no response field found, use the text after removing code blocks
                # Remove any trailing JSON-like structures first
                cleaned_text = re.sub(r',\s*"should_progress".*$', '', text)
                cleaned_text = re.sub(r',\s*"state_field".*$', '', cleaned_text)
                result["response"] = cleaned_text.strip()
                logger.info(f"Brute force: used cleaned text as response: '{result['response'][:30]}...'")
        
        # Extract should_progress field
        progress_pattern = r'"should_progress"\s*:\s*"(yes|no)"'
        progress_match = re.search(progress_pattern, text, re.IGNORECASE)
        if progress_match:
            result["should_progress"] = progress_match.group(1).lower()
            logger.info(f"Brute force: extracted should_progress field: '{result['should_progress']}'")
        else:
            # Try alternative pattern without quotes
            alt_progress_pattern = r'"should_progress"\s*:\s*(true|false|yes|no)'
            alt_progress_match = re.search(alt_progress_pattern, text, re.IGNORECASE)
            if alt_progress_match:
                value = alt_progress_match.group(1).lower()
                result["should_progress"] = "yes" if value in ["true", "yes"] else "no"
                logger.info(f"Brute force: extracted should_progress field with alt pattern: '{result['should_progress']}'")
        
        # Extract state_field if present
        state_pattern = r'"state_field"\s*:\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
        state_match = re.search(state_pattern, text)
        if state_match:
            state_field_str = state_match.group(1)
            # Try multiple approaches to parse the state field
            try:
                # Try json.loads first
                try:
                    result["state_field"] = json.loads(state_field_str)
                    logger.info(f"Brute force: extracted and parsed state_field with json.loads: {result['state_field']}")
                except json.JSONDecodeError:
                    # Try with ast.literal_eval as fallback
                    import ast
                    try:
                        result["state_field"] = ast.literal_eval(state_field_str)
                        logger.info(f"Brute force: extracted state_field with ast.literal_eval: {result['state_field']}")
                    except Exception:
                        # If both parsing methods fail, try more manual extraction for simple cases
                        simple_state_fields = {}
                        field_pattern = r'"([^"]+)"\s*:\s*"([^"]*)"'
                        for field_match in re.finditer(field_pattern, state_field_str):
                            field_name, field_value = field_match.groups()
                            simple_state_fields[field_name] = field_value
                        
                        if simple_state_fields:
                            result["state_field"] = simple_state_fields
                            logger.info(f"Brute force: extracted state_field with regex: {result['state_field']}")
            except Exception as e:
                logger.warning(f"Brute force: Could not parse state_field: {e}")
                
        # Clean up the response field if it contains unmatched quotes or JSON fragments
        if result["response"] and ('"' in result["response"] or '{' in result["response"] or '}' in result["response"]):
            # Remove any trailing JSON fragments
            result["response"] = re.sub(r',\s*"should_progress".*$', '', result["response"])
            result["response"] = re.sub(r',\s*"state_field".*$', '', result["response"])
            
            # Remove any trailing quotes or commas
            result["response"] = re.sub(r'",?\s*$', '', result["response"])
            
            logger.info(f"Brute force: cleaned response field: '{result['response'][:30]}...'")
        
        return result
    except Exception as e:
        logger.error(f"Brute force JSON extraction failed: {e}", exc_info=True)
        return result

def clean_json_for_parsing(text: str) -> str:
    """Clean JSON string by escaping or removing control characters that cause parsing errors"""
    logger.info(f"clean_json_for_parsing input: '{text[:30]}...'")
    
    if not text:
        logger.warning("Empty text passed to clean_json_for_parsing, returning empty JSON object")
        return "{}"
    
    # First, check if the text contains JSON fields mixed with the message
    # For example: "Okay, I understand you'll upload your National ID document tomorrow. Just to reiterate the benefits of e-documentation:", "should_progress": "no", "state_field
    mixed_content_pattern = r'^(.*?)(?:,\s*"should_progress"|,\s*"state_field")'
    mixed_content_match = re.search(mixed_content_pattern, text, re.DOTALL)
    if mixed_content_match and ('"should_progress"' in text or '"state_field"' in text):
        logger.info("Detected mixed content with JSON fields. Attempting to reconstruct proper JSON.")
        try:
            main_content = mixed_content_match.group(1).strip()
            # Clean up any trailing quotes or commas
            main_content = re.sub(r'",?\s*$', '', main_content)
            # Clean up any leading quotes
            main_content = re.sub(r'^"', '', main_content)
            
            # Now reconstruct proper JSON
            reconstructed_json = '{"response": "' + main_content.replace('"', '\\"') + '"'
            
            # Extract should_progress if present
            progress_pattern = r'"should_progress"\s*:\s*"?(yes|no|true|false)"?'
            progress_match = re.search(progress_pattern, text, re.IGNORECASE)
            if progress_match:
                progress_value = progress_match.group(1).lower()
                progress_value = "yes" if progress_value in ["true", "yes"] else "no"
                reconstructed_json += f', "should_progress": "{progress_value}"'
            
            # Extract state_field if present
            state_pattern = r'"state_field"\s*:\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})'
            state_match = re.search(state_pattern, text, re.DOTALL)
            if state_match:
                state_field = state_match.group(1).strip()
                reconstructed_json += f', "state_field": {state_field}'
            else:
                # If no state_field found but there's a reference to it, add an empty one
                if '"state_field"' in text:
                    reconstructed_json += ', "state_field": {}'
            
            reconstructed_json += '}'
            logger.info(f"Reconstructed JSON from mixed content: '{reconstructed_json[:50]}...'")
            return reconstructed_json
        except Exception as e:
            logger.warning(f"Failed to reconstruct JSON from mixed content: {e}")
    
    # Check if the text still contains markdown code block delimiters
    if text.strip().startswith("```"):
        logger.warning("Text still contains markdown delimiters, removing them")
        # Extract from code blocks again to be sure
        code_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
        code_block_match = re.search(code_block_pattern, text)
        if code_block_match:
            text = code_block_match.group(1).strip()
            logger.info(f"Extracted from markdown in clean_json_for_parsing: '{text[:30]}...'")
    
    # Replace non-ascii characters, including emojis, with their Unicode escape sequences
    text = text.encode('unicode_escape').decode('utf-8')
    
    # Replace literal newlines with escaped newlines
    text = text.replace('\n', '\\n')
    
    # Replace tabs with spaces
    text = text.replace('\t', ' ')
    
    # Handle escaped quotes that might be causing issues
    text = text.replace('\\"', '"')
    
    # Make sure double quotes are used for property names (not single quotes)
    # This is a common issue with JSON - using regex to find 'key': patterns and replace with "key":
    text = re.sub(r'\'([^\']+)\'(\s*:)', r'"\1"\2', text)
    
    # Ensure we don't have any unescaped control characters
    text = re.sub(r'[\x00-\x1F\x7F-\x9F]', '', text)
    
    # Verify the JSON structure has balanced braces
    if text.count('{') != text.count('}'):
        logger.warning(f"Unbalanced braces in JSON: {text.count('{')} opening vs {text.count('}')} closing")
        # Try to extract just the first complete JSON object if there are unbalanced braces
        start_idx = text.find('{')
        if start_idx >= 0:
            # Count open and close braces to find the matching end brace
            open_count = 0
            for i in range(start_idx, len(text)):
                if text[i] == '{':
                    open_count += 1
                elif text[i] == '}':
                    open_count -= 1
                    if open_count == 0:
                        # Found the matching closing brace
                        text = text[start_idx:i+1]
                        logger.info(f"Extracted balanced JSON object: '{text[:30]}...'")
                        break
    
    # Final check for valid JSON structure
    if not (text.startswith('{') and text.endswith('}')):
        logger.warning(f"JSON doesn't start with {{ and end with }}: '{text[:30]}...'")
        # As a last resort, try to wrap non-JSON text in a valid JSON structure
        if not text.startswith('{'):
            # This might be just a plain text response, try to create a JSON object with it
            if '"response"' not in text and '"should_progress"' not in text and '"state_field"' not in text:
                # Escape any quotes in the text
                escaped_text = text.replace('"', '\\"')
                text = f'{{"response": "{escaped_text}", "should_progress": "no", "state_field": {{}}}}'
                logger.info(f"Created JSON wrapper around plain text: '{text[:50]}...'")
    
    logger.info(f"clean_json_for_parsing output: '{text[:30]}...'")
    return text

def analyze_image(image_data, user_query=None, user_context=None):
    """Analyze an uploaded image to extract user information
    
    Args:
        image_data: Base64 encoded image data
        user_query: Optional specific query to guide the analysis
        user_context: Optional user context to update
        
    Returns:
        Dict with analysis results and success status
    """
    try:
        # Define function to clean and extract valid JSON from responses
        def clean_json_string(text):
            """Extract valid JSON from potentially malformed responses"""
            # Check for JSON within triple backticks
            import re
            json_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
            match = re.search(json_pattern, text)
            
            if match:
                # Found JSON inside triple backticks, try to parse it
                try:
                    json_text = match.group(1).strip()
                    extracted_data = json.loads(json_text)
                    logger.info(f"Successfully extracted JSON from triple backticks")
                    
                    # Make sure all required fields are present with proper field names
                    normalized_data = {}
                    
                    # Map common field name variations to our expected fields
                    field_mappings = {
                        'name': ['name', 'full_name'],
                        'id_number': ['id_number', 'serial_number', 'identity_number', 'huduma_number', 'id_no'],
                        'country': ['country', 'nation'],
                        'state': ['state', 'province', 'region'],
                        'city': ['city', 'city_of_issue', 'town', 'place_of_birth'],
                        'zip_code': ['zip_code', 'postal_code', 'zip'],
                        'dob': ['dob', 'date_of_birth', 'birth_date'],
                        'gender': ['gender', 'sex'],
                        'address': ['address', 'residence'],
                        'phone': ['phone', 'phone_number', 'telephone', 'mobile']
                    }
                    
                    # Apply mappings to normalize field names
                    for target_field, possible_fields in field_mappings.items():
                        for field in possible_fields:
                            if field in extracted_data and extracted_data[field]:
                                normalized_data[target_field] = extracted_data[field]
                                break
                    
                    # Ensure all required fields exist
                    for field in ['name', 'id_number', 'country', 'state', 'city', 'zip_code', 'dob', 'gender', 'address', 'phone']:
                        if field not in normalized_data:
                            normalized_data[field] = ""
                    
                    return normalized_data
                except json.JSONDecodeError as e:
                    logger.warning(f"Found JSON in triple backticks but failed to parse: {e}")
            
            # If we didn't find valid JSON in backticks, try direct parsing
            try:
                # First try direct JSON parsing
                import json
                return json.loads(text)
            except:
                # If the response is not valid JSON, try to extract structured information
                # and convert it to JSON format ourselves
                
                # Check if it's a list format with dashes
                lines = text.strip().split('\n')
                extracted_data = {}
                
                # Extract information from bullet points or list format
                for line in lines:
                    line = line.strip()
                    if line.startswith('- ') or line.startswith('* '):
                        line = line[2:].strip()  # Remove the bullet point
                        if ':' in line:
                            key, value = line.split(':', 1)
                            # Clean and normalize keys
                            key = key.strip().lower().replace(' ', '_')
                            if 'identity' in key or 'id_no' in key or 'id_number' in key:
                                key = 'id_number'
                            elif 'name' in key or 'full_name' in key:
                                key = 'name'
                            elif 'birth' in key and 'date' in key:
                                key = 'dob'
                            elif 'sex' in key or 'gender' in key:
                                key = 'gender'
                            elif 'place_of_birth' in key:
                                # Extract city or region from place of birth
                                value_parts = value.strip().split()
                                if value_parts:
                                    extracted_data['city'] = value_parts[-1]
                            
                            # Add the extracted value
                            extracted_data[key] = value.strip()
                
                # Try to infer country from the data or description
                if 'country' not in extracted_data:
                    if 'kenya' in text.lower() or 'kenyan' in text.lower():
                        extracted_data['country'] = 'Kenya'
                    elif 'uganda' in text.lower() or 'ugandan' in text.lower():
                        extracted_data['country'] = 'Uganda'
                    elif 'tanzania' in text.lower() or 'tanzanian' in text.lower():
                        extracted_data['country'] = 'Tanzania'
                    else:
                        extracted_data['country'] = ''
                
                # Add other required fields if missing
                for field in ['name', 'id_number', 'country', 'state', 'city', 'zip_code', 'dob', 'gender', 'address', 'phone']:
                    if field not in extracted_data:
                        extracted_data[field] = ""
                
                logger.info(f"Manually extracted data from text: {extracted_data}")
                return extracted_data
        
        # If no specific query is provided, use a default query that emphasizes mandatory fields and JSON format
        if not user_query:
            user_query = """
            Analyze this National ID document and extract the following information in JSON format.
            The MANDATORY fields are: name, id_number, country, state, city, and zip_code.
            Also extract if available: date of birth (dob), gender, address, and phone number.
            
            IMPORTANT: Your response MUST be in valid JSON format with these exact field names.
            Example format:
            {
              "name": "John Doe",
              "id_number": "12345678",
              "country": "Kenya",
              "state": "Central",
              "city": "Nairobi",
              "zip_code": "00100",
              "dob": "01.01.1990",
              "gender": "Male",
              "address": "123 Main St",
              "phone": "+254123456789"
            }
            """
        
        # Check that image_data is not empty
        if not image_data:
            raise ValueError("No image data provided")
            
        # Azure OpenAI API call with direct credentials
        from openai import AzureOpenAI
        
        vision_client = AzureOpenAI(
            api_key="********************************",
            api_version="2024-02-01",
            azure_endpoint="https://adya-openai-aus.openai.azure.com/"
        )
        
        # Make the API call with explicit instructions for JSON output
        response = vision_client.chat.completions.create(
            model="gpt4vision_deployed",
            messages=[
                {"role": "system", "content": """You are a document analyzer specialized in extracting structured information from IDs and documents. 
                
                EXTREMELY IMPORTANT:
                1. You MUST ALWAYS respond in valid JSON format
                2. Use ONLY the EXACT field names requested by the user
                3. DO NOT wrap your JSON in markdown code blocks or backticks
                4. Return ONLY the raw JSON object without any additional text
                5. If a field is not visible or cannot be determined, include it with an empty string value
                
                Example of correct response:
                {"name":"John Doe","id_number":"123456","country":"Kenya","state":"Central","city":"Nairobi","zip_code":"00100","dob":"01.01.1990","gender":"Male","address":"","phone":""}"""},
                {"role": "user", "content": [
                    {"type": "text", "text": user_query},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}}
                ]}
            ],
            max_tokens=4000
        )
        
        logger.info(f"Vision API response: {response.choices[0].message.content}")
        
        # Extract the JSON content from the response
        extracted_info = clean_json_string(response.choices[0].message.content)
        
        # Determine missing mandatory fields
        mandatory_fields = ["name", "id_number", "country", "state", "city", "zip_code"]
        missing_fields = [field for field in mandatory_fields if not extracted_info.get(field)]
        
        # Update user context if provided
        if user_context:
            user_context.extracted_info = extracted_info
            user_context.missing_fields = missing_fields
        
        # Prepare the response
        if not missing_fields:
            return {
                "success": True,
                "message": "Successfully extracted all required information.",
                "extracted_info": extracted_info,
                "missing_fields": []
            }
        else:
            return {
                "success": False,
                "message": f"Could not extract all required fields. Please provide: {', '.join(missing_fields)}",
                "extracted_info": extracted_info,
                "missing_fields": missing_fields
            }
            
    except Exception as e:
        logger.error(f"Error in image analysis: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": "Unable to process the image. Please provide your information manually.",
            "missing_fields": ["name", "id_number", "country", "state", "city", "zip_code"],
            "extracted_info": {
                "name": "",
                "id_number": "",
                "country": "",
                "state": "",
                "city": "",
                "zip_code": "",
                "dob": "",
                "gender": "",
                "address": "",
                "phone": ""
            }
        }

# Load bike specifications from JSON file
def load_bike_specs():
    try:
        import os
        # Get the directory of the current script
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Go up to the ai_backend directory - 2 levels from services
        backend_dir = os.path.dirname(os.path.dirname(current_dir))
        bike_specs_path = os.path.join(backend_dir, 'bike_specs.json')
        
        logger.info(f"Looking for bike_specs.json at: {bike_specs_path}")
        
        with open(bike_specs_path, 'r') as f:
            data = json.load(f)
            # Log the loaded data for debugging
            logger.info(f"Successfully loaded bike specs with {len(data.get('BIKE_SPECS', {}))} models")
            # Ensure we have a valid list of bike models
            if 'BIKE_SPECS' in data and isinstance(data['BIKE_SPECS'], dict):
                # Properly extract bike models from the data
                bike_models = list(data.get('BIKE_SPECS', {}).keys())
                logger.info(f"Found bike models: {bike_models}")
                # Return the data
                return data
            else:
                logger.error("Invalid bike specs format: 'BIKE_SPECS' key missing or not a dictionary")
                return {"BIKE_SPECS": {}, "SPEC_ALIASES": {}}
    except FileNotFoundError:
        logger.error(f"bike_specs.json file not found at {bike_specs_path}")
        # Try alternative locations
        try:
            import os
            for potential_path in [
                os.path.join(os.getcwd(), 'apps', 'ai_backend', 'bike_specs.json'),
                os.path.join(os.getcwd(), 'ai_backend', 'bike_specs.json'),
                os.path.join(os.getcwd(), 'bike_specs.json')
            ]:
                logger.info(f"Trying alternative path: {potential_path}")
                if os.path.exists(potential_path):
                    logger.info(f"Found bike_specs.json at: {potential_path}")
                    with open(potential_path, 'r') as f:
                        return json.load(f)
            
            # If we get here, we couldn't find the file
            logger.error("Couldn't find bike_specs.json in any location")
            return {"BIKE_SPECS": {}, "SPEC_ALIASES": {}}
        except Exception as e:
            logger.error(f"Error finding alternative location: {str(e)}")
            return {"BIKE_SPECS": {}, "SPEC_ALIASES": {}}
    except json.JSONDecodeError:
        logger.error("Error parsing bike_specs.json")
        return {"BIKE_SPECS": {}, "SPEC_ALIASES": {}}

BIKE_DATA = load_bike_specs()
BIKE_MODELS = list(BIKE_DATA.get("BIKE_SPECS", {}).keys())
SPEC_ALIASES = BIKE_DATA.get("SPEC_ALIASES", {})

# Node types in the flowchart
class NodeType(str, Enum):
    ACTION = "action"          # Rectangle/process - take action
    DECISION = "decision"      # Diamond - make decision
    START_END = "start_end"    # Rounded rectangle - start/end points

# LLM Call tracking for debug purposes
class LLMCall(BaseModel):
    timestamp: float
    node_id: str
    node_name: str
    node_type: str
    prompt: str
    user_input: str
    response: str
    tokens_used: int = 0
    duration_ms: float = 0
    model: str = MODEL  # Track which model was used for the call

# Define the conversation pipeline nodes based on the flowchart
class PipelineNode(BaseModel):
    id: str
    name: str
    type: NodeType
    prompt: str
    next_nodes: Dict[str, str] = {}  # Maps result -> next node id
    persistence_condition: Optional[str] = None  # Condition to evaluate before transitioning
    
    def get_next_node(self, result: str) -> Optional[str]:
        """Get the next node ID based on the result"""
        if self.type == NodeType.DECISION:
            return self.next_nodes.get(result)
        elif self.type == NodeType.ACTION:
            return self.next_nodes.get("default")
        return None

class UserContext(BaseModel):
    """Stores information about the user throughout the conversation"""
    name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    selected_model: Optional[str] = None
    preferences: Dict[str, Any] = {}
    conversation_history: List[Dict[str, str]] = []
    current_node_id: str = "start"
    node_interactions: Dict[str, int] = {}  # Track interactions per node
    ready_to_transition: bool = False  # Flag to indicate if ready to move to next node
    is_interested: Optional[bool] = None
    has_documents: Optional[bool] = None
    wants_emi: Optional[bool] = None
    scheduled_time: Optional[str] = None
    model_price: Optional[float] = None
    compared_models: List[str] = []
    # Added field for data sharing consent
    data_sharing_consent: bool = True
    # Added field for showroom selection
    selected_showroom: Optional[str] = None
    # Added fields for lead data
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    salutation: Optional[str] = None
    gender: Optional[str] = None
    country: Optional[str] = None
    state: Optional[str] = None
    city: Optional[str] = None
    sms_language: str = "English"
    sacco_name: Optional[str] = None
    building: Optional[str] = None
    street: Optional[str] = None
    zip_code: Optional[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    verbal_language: str = "English"
    birth_date: Optional[str] = None
    national_id: Optional[str] = None
    kcb_account: Optional[str] = None
    kra_pin: Optional[str] = None
    identification_available: str = "No"
    extracted_from_image: Dict[str, Any] = Field(default_factory=dict)
    # Added fields for image processing
    missing_fields: List[str] = Field(default_factory=list)
    extracted_info: Dict[str, Any] = Field(default_factory=dict)
    image_upload_attempts: int = 0
    id_upload_complete: bool = False
    image_uploaded: bool = False  # Flag to track if image was actually uploaded
    # Added field for lead qualification tracking
    lead_qualification_done: bool = False  # Flag to track if lead qualification has been done
    # Added field for reference_id
    reference_id: Optional[str] = None  # Reference ID from the payload
    
    def update_from_image_data(self, image_data: Dict[str, Any]):
        """Update user context with data extracted from ID image"""
        if not image_data:
            return
        
        self.extracted_info.update(image_data)
        self.extracted_from_image.update(image_data)
        
        # Update individual fields from extracted data
        if 'name' in image_data and image_data['name']:
            name_parts = image_data['name'].split()
            if len(name_parts) > 0:
                self.first_name = name_parts[0]
            if len(name_parts) > 1:
                self.last_name = " ".join(name_parts[1:])
        
        if 'id_number' in image_data and image_data['id_number']:
            self.national_id = image_data['id_number']
            self.identification_available = "Yes"
            
        if 'dob' in image_data and image_data['dob']:
            self.birth_date = image_data['dob']
            
        if 'gender' in image_data and image_data['gender']:
            self.gender = image_data['gender']
            
        if 'address' in image_data and image_data['address']:
            self.address = image_data['address']
            
        if 'country' in image_data and image_data['country']:
            self.country = image_data['country']
            
        if 'state' in image_data and image_data['state']:
            self.state = image_data['state']
            
        if 'city' in image_data and image_data['city']:
            self.city = image_data['city']
            
        if 'zip_code' in image_data and image_data['zip_code']:
            self.zip_code = image_data['zip_code']
            
        if 'phone' in image_data and image_data['phone']:
            self.phone = image_data['phone']
            
        # Mark ID upload as complete
        self.id_upload_complete = True
        self.image_uploaded = True
        
        # Track that we attempted to process an image
        self.image_upload_attempts += 1
    
    def has_all_required_fields(self) -> bool:
        """
        Always return True to indicate all required fields are present
        This bypasses missing field validation as requested
        """
        return True
    
    def get_missing_fields(self) -> List[str]:
        """
        Always return an empty list of missing fields
        This bypasses missing field tracking as requested
        """
        return []
    
    def update_missing_field(self, field: str, value: str):
        """
        Update a field that was previously missing
        We maintain this function for compatibility but it no longer tracks missing fields
        """
        if not value.strip():
            return
            
        if field == "name":
            name_parts = value.split()
            if len(name_parts) > 0:
                self.first_name = name_parts[0]
            if len(name_parts) > 1:
                self.last_name = " ".join(name_parts[1:])
        elif field == "id_number":
            self.national_id = value
            self.identification_available = "Yes"
        elif field == "dob":
            self.birth_date = value
        elif field == "gender":
            self.gender = value
        elif field == "address":
            self.address = value
            
            # Try to extract city, state, country from address
            address_parts = value.split(",")
            if len(address_parts) >= 3:
                self.city = address_parts[-3].strip()
                self.state = address_parts[-2].strip()
                self.country = address_parts[-1].strip()
            elif len(address_parts) >= 2:
                self.city = address_parts[-2].strip()
                self.country = address_parts[-1].strip()
        elif field == "country":
            self.country = value
        elif field == "state":
            self.state = value
        elif field == "city":
            self.city = value
        elif field == "zip_code":
            self.zip_code = value
        
        # We no longer recalculate missing fields
        # self.missing_fields = self.get_missing_fields()
    
    def add_message(self, role: str, content: str):
        """Add a message to conversation history"""
        self.conversation_history.append({"role": role, "content": content})
    
    def get_recent_history(self, num_messages: int = 5) -> List[Dict[str, str]]:
        """Get the most recent messages from conversation history"""
        return self.conversation_history[-num_messages:] if len(self.conversation_history) > num_messages else self.conversation_history
    
    def increment_node_interaction(self, node_id: str):
        """Increment the interaction count for the current node"""
        if node_id not in self.node_interactions:
            self.node_interactions[node_id] = 0
        self.node_interactions[node_id] += 1
    
    def get_node_interactions(self, node_id: str) -> int:
        """Get the number of interactions for a specific node"""
        return self.node_interactions.get(node_id, 0)
    
    def add_compared_model(self, model: str):
        """Add a model to the list of compared models"""
        if model not in self.compared_models:
            self.compared_models.append(model)
    
    def get_compared_models(self) -> List[str]:
        """Get the list of models the user has compared"""
        return self.compared_models

# Define the standalone image upload handler function before the SalePipeline class
def handle_image_upload(image_data: str, pipeline_instance):
    """Handle image upload for the pipeline"""
    try:
        # Create an enhanced query with context from the user state
        enhanced_query = """
        Analyze this National ID document and extract the following information in JSON format.
        The MANDATORY fields are: name, id_number, country, state, city, and zip_code.
        Also extract if available: date of birth (dob), gender, address, and phone number.
        
        IMPORTANT: Your response MUST be in valid JSON format with these exact field names.
        """
        
        context = pipeline_instance.context
        
        # Format the user's existing information to enhance the context for the image analyzer
        if context.first_name or context.last_name:
            name = f"{context.first_name or ''} {context.last_name or ''}".strip()
            enhanced_query += f"\n\nNote: The user has previously identified as {name}."
            
        if context.national_id:
            enhanced_query += f"\n\nNote: The user has previously provided ID number: {context.national_id}."
            
        if context.country:
            enhanced_query += f"\n\nNote: The user has previously indicated country: {context.country}."
            
        # Call the image processing function
        result = analyze_image(
            image_data=image_data,
            user_query=enhanced_query,
            user_context=context
        )
        
        # Update context with extracted information
        if result.get("extracted_info"):
            context.update_from_image_data(result["extracted_info"])
            
        # Set ID upload as complete
        context.id_upload_complete = True
        context.image_uploaded = True
        
        # For the improved flow, we don't automatically set ready_to_transition
        # This allows the process_national_id node to explicitly handle the transition
        
        return {
            "success": True,
            "extracted_info": result.get("extracted_info", {}),
            "missing_fields": context.missing_fields
        }
    except Exception as e:
        logger.error(f"Error handling image upload: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": "Error processing the image",
            "error": str(e)
        }

class SalePipeline:
    def __init__(self):
        """Initialize the pipeline with nodes and context"""
        self.context = UserContext()
        self.llm_call_count = 0
        self.llm_calls = []
        self.nodes = {}  # Initialize empty nodes dictionary
        
        # Load bike data directly to ensure fresh data
        self.bike_data = load_bike_specs()
        # Explicitly extract bike models from bike data
        self.bike_models = list(self.bike_data.get("BIKE_SPECS", {}).keys())
        
        # Log the loaded bike models for debugging
        logger.info(f"Initialized SalePipeline with {len(self.bike_models)} bike models: {self.bike_models}")
        
        # Feature categories for grouping bike specifications
        self.feature_categories = {
            "performance": ["MOTOR_POWER", "MOTOR_TYPE"],
            "battery": ["BATTERY_CAPACITY", "BATTERY_COMPATIBILITY", "RANGE", "FAST_CHARGING"],
            "brakes": ["FRONT_BRAKE", "REAR_BRAKE", "CBS"],
            "suspension": ["FORKS", "REAR_SUSPENSION"],
            "display": ["DISPLAY", "USB_CHARGER"],
            "accessories": ["SAREE_GUARD", "HOME_CHARGER", "EXTERNAL_CHARGING"]
        }
        self._initialize_pipeline()  # Always initialize nodes in constructor

    def _generate_bike_details(self, num_bikes: int = 3) -> str:
        """Generate detailed specifications for a number of bikes"""
        bike_specs = self.bike_data.get("BIKE_SPECS", {})
        
        if not bike_specs:
            return "Our bike models information is currently unavailable."
        
        # Choose a diverse selection of bikes (if we have enough)
        selected_bikes = []
        if len(bike_specs) <= num_bikes:
            selected_bikes = list(bike_specs.keys())
        else:
            # Try to select diverse bikes (different series)
            series_bikes = {}
            for model in bike_specs.keys():
                series = model.split(" ")[0]  # Extract series name (e.g., "EKON" from "EKON 450")
                if series not in series_bikes:
                    series_bikes[series] = []
                series_bikes[series].append(model)
            
            # Pick one from each series if possible
            for series, models in series_bikes.items():
                if len(selected_bikes) < num_bikes:
                    selected_bikes.append(models[0])  # Add first model from each series
            
            # If we still need more bikes, add randomly
            remaining_models = [m for m in bike_specs.keys() if m not in selected_bikes]
            while len(selected_bikes) < num_bikes and remaining_models:
                selected_bikes.append(remaining_models.pop(0))
        
        # Generate detailed text for each selected bike
        details = "Available models with specifications:\n\n"
        
        for model in selected_bikes:
            specs = bike_specs.get(model, {})
            details += f"## {model}\n"
            
            # Add key specifications
            key_specs = [
                ("Range", specs.get("RANGE", "N/A")),
                ("Motor Power", specs.get("MOTOR_POWER", "N/A")),
                ("Motor Type", specs.get("MOTOR_TYPE", "N/A")),
                ("Payload Capacity", specs.get("PAYLOAD_CAPACITY", "N/A")),
                ("Suitable Terrain", specs.get("SUITABLE_TERRAIN", "N/A")),
                ("Battery Capacity", specs.get("BATTERY_CAPACITY", ["N/A"])[0] if isinstance(specs.get("BATTERY_CAPACITY", []), list) else specs.get("BATTERY_CAPACITY", "N/A")),
                ("Fast Charging", specs.get("FAST_CHARGING", "N/A"))
            ]
            
            for name, value in key_specs:
                details += f"- {name}: {value}\n"
            
            details += "\n"
        
        return details

    def _initialize_pipeline(self):
        """Create the pipeline nodes based on the flowchart"""
        # Generate bike details for the prompt (no longer used as it's in the common header now)
        
        self.nodes = {
            # Starting node - Initiate conversation
            "start": PipelineNode(
                id="start",
                name="Initiate Conversation",
                type=NodeType.ACTION,
                prompt="""You are a friendly, casual sales representative for Spiro electric bikes. 
                Your task is to initiate a conversation with a potential customer.
                Be warm and welcoming but not overly enthusiastic.
                Ask if they're interested in learning about Spiro electric bikes.
                Keep your response brief - no more than 2-3 short sentences.
                If this is your first message, briefly introduce yourself as a Spiro representative.""",
                next_nodes={"default": "interested_decision"},
                persistence_condition="always"  # No need for persistence here
            ),
            
            # Decision node - Is the user interested?
            "interested_decision": PipelineNode(
                id="interested_decision",
                name="Determine Interest",
                type=NodeType.DECISION,
                prompt="""Determine if the user is interested in Spiro electric bikes.
                Return EXACTLY "yes" if any of these are true:
                - They express any interest in Spiro bikes OR electric vehicles in general
                - They seem curious about electric bikes or ask questions about them
                - Their message is neutral or ambiguous
                - They show hesitation but don't explicitly reject
                - They want to know more information
                
                Return EXACTLY "no" ONLY if:
                - They explicitly say they're not interested 
                - They express anger or hate
                - They clearly state they don't want to continue the conversation
                - They use strong negative language about the product

                When in doubt, you should lean towards "yes" to keep the conversation going.
                The goal is to give users more opportunity to learn about Spiro bikes.""",
                next_nodes={"yes": "customer_engagement", "no": "share_ev_benefits"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Customer Engagement
            "customer_engagement": PipelineNode(
                id="customer_engagement",
                name="Customer Engagement",
                type=NodeType.ACTION,
                prompt="""You are a personable, enthusiastic Spiro sales representative establishing rapport with a potential customer.
                
                Your goals for this conversation:
                1. Build a genuine connection with the customer
                2. Learn about how many kilometers they cover on average per day
                3. Understand what their typical travel range needs are
                4. Discover what's most important to them (cost, features, sustainability, etc.)
                5. Share your authentic enthusiasm about Spiro bikes
                
                IMPORTANT CONVERSATION TECHNIQUES:
                - Ask directly about range/how many kilometres covered on average per day rather than use cases
                - Share a brief personal anecdote about why you love Spiro bikes
                - Use active listening techniques (acknowledge their points before responding)
                - Match their communication style (formal/casual, detailed/big picture)
                - Find common ground to build rapport
                
                CRITICAL: Your FIRST question to the customer should be asking specifically about how many kilometers they travel on average per day.
                
                Keep the conversation natural and engaging. Don't rush to talk about specific models yet.
                When you feel you've established good rapport (usually after 1-2 exchanges), progress to showing models.
                If the user wishes to see the models, progress to the display_models node. Don't show the models in this node, casually proceed to the next node.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your engaging conversation here",
                  "should_progress": "yes" 
                }""",
                next_nodes={"default": "display_models"},
                persistence_condition="interactions>=1"  # Allow at least one interaction before considering transition
            ),

            # Action node - Share EV Benefits
            "share_ev_benefits": PipelineNode(
                id="share_ev_benefits",
                name="Share EV Benefits",
                type=NodeType.ACTION,
                prompt="""You are a charismatic and persuasive Spiro sales specialist - the BEST in the world at converting leads.
                Your style is casual but incredibly convincing. You're not just sharing benefits, you're painting an exciting future.
                
                Based on the number of interactions with this node, share different benefits progressively:
                
                First interaction (interactions=1):
                - Focus on the immediate personal benefits:
                  * "Hey, let me tell you something cool about EVs that most people don't realize..."
                  * Emphasize MASSIVE fuel cost savings (up to 90% less than petrol!)
                  * Highlight how maintenance costs are practically non-existent
                  * Share a quick success story of someone saving big money
                
                Second interaction (interactions=2):
                - Focus on the experience and status:
                  * Talk about the smooth, powerful acceleration
                  * Mention the head-turning modern design
                  * Emphasize being part of an exclusive group of early adopters
                  * Share how other riders feel like they're living in the future
                
                Third interaction (interactions=3):
                - Focus on future-proofing and smart investment:
                  * Government incentives and tax benefits
                  * Rising petrol costs vs stable electricity costs
                  * Growing charging infrastructure
                  * Increasing resale value of EVs
                
                Fourth+ interaction (interactions>=4):
                - Focus on FOMO and urgency:
                  * Limited time offers
                  * Growing waitlists
                  * Special early-adopter benefits ending soon
                  * Exclusive financing options available now
                
                IMPORTANT STYLE GUIDELINES:
                1. Be casual and conversational - talk like a friend, not a salesperson
                2. Use phrases like "Look, here's the thing..." or "Between you and me..."
                3. Create FOMO (Fear of Missing Out) without being pushy
                4. Share real numbers and facts but in a casual way
                5. Always end with a direct question "Are you interested now?" to gauge interest
                6. Keep each message short and punchy - don't overwhelm
                7. Use emotive language that creates excitement
                
                CRITICAL: In EVERY interaction, EXPLICITLY ask if they're interested now after sharing the benefits. 
                Use a direct question like "Does this sound interesting to you now?" or "Are these benefits convincing enough for you?"
                
                IMPORTANT TRANSITION LOGIC:
                1. ANALYZE their response for ANY sign of interest, no matter how small.
                2. If they show ANY interest at ANY point (even after just the first interaction), you MUST transition to the next node.
                3. Don't keep sharing more benefits if they're already showing interest - move forward immediately.
                4. Only stay in this node if they've explicitly rejected or shown no interest in the benefits so far.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your persuasive message sharing benefits + explicitly asking if they're interested",
                  "should_progress": "yes if they show ANY interest whatsoever, no ONLY if they explicitly reject"
                }""",
                next_nodes={"default": "post_benefits_decision"},
                persistence_condition="interactions>=1 && !user_showing_interest"  # Only stay if there's been at least one interaction AND user isn't showing interest
            ),

            # Decision node - After Benefits
            "post_benefits_decision": PipelineNode(
                id="post_benefits_decision",
                name="Check Interest After Benefits",
                type=NodeType.DECISION,
                prompt="""You are the world's best sales specialist at detecting buying signals.
                
                Analyze the user's response for ANY hint of interest or curiosity.
                Return EXACTLY "yes" if they show ANY of these signals:
                - Express even slight interest in learning more
                - Ask ANY questions about EVs or benefits
                - Show curiosity about specific features
                - Mention anything positive about EVs
                - Use words like "maybe", "perhaps", "interesting"
                - Want to know more about pricing or options
                - Ask about next steps or what to do next
                - Show any willingness to proceed
                
                Return EXACTLY "no" ONLY if they:
                - Explicitly say "no" or "not interested"
                - Clearly state they want to end the conversation
                
                When in doubt, return "yes" - we want to keep the conversation going!
                
                Remember: Your job is to find ANY excuse to continue the conversation.""",
                next_nodes={"yes": "display_models", "no": "share_ev_benefits"},  # Direct to ID documentation if interested
                persistence_condition="always"
            ),

            
            # Action node - Prepare for Documents
            "prepare_for_documents": PipelineNode(
                id="prepare_for_documents",
                name="Prepare for Documents",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro application specialist.
                
                IMPORTANT: First, review the conversation history to understand the context, especially the user's response to the test ride offer.
                
                Transition smoothly to document preparation regardless of their test ride decision:
                
                1. Acknowledge their test ride response briefly:
                   - If they showed interest: "Great! We'll set up your test ride. Now, to move forward with your application process..."
                   - If they declined: "I understand. If you change your mind about the test ride later, that's no problem. Now, to proceed with your application..."
                
                2. Explain why we need to collect some documentation:
                   - To process their application efficiently
                   - To verify their identity for security purposes
                   - To prepare for financing options if interested
                
                3. Mention that the first key document we'll need is their National ID
                
                4. Make it conversational and friendly
                
                Be clear that this is a separate step from the test ride decision. Do not confuse their test ride response with their willingness to provide documentation.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user goes here",
                  "should_progress": "yes"
                }""",
                next_nodes={"default": "id_documentation_check"},
                persistence_condition="interactions>=1"
            ),
            
            # Action node - Aggressive Convincing
            "aggressive_convincing": PipelineNode(
                id="aggressive_convincing",
                name="Aggressive Convincing",
                type=NodeType.ACTION,
                prompt="""You are the ULTIMATE spiros closer - when others give up, you make the sale happen.
                
                This is your LAST CHANCE to convert this prospect. Pull out all the stops:
                
                1. Create massive FOMO:
                   - "Look, I probably shouldn't tell you this, but..."
                   - "Between you and me, we have something special coming up..."
                   - "I've seen so many people regret not trying it when they had the chance..."
                
                2. Use social proof:
                   - Share an impressive success story
                   - Mention growing waitlists
                   - Talk about how existing customers are saving massive money
                
                3. Create urgency:
                   - Limited-time offers ending soon
                   - Fuel prices expected to rise again
                   - Special financing rates about to change
                
                4. Make an irresistible offer:
                   - VIP test ride experience
                   - Special attention from our expert team
                   - Exclusive benefits for early adopters
                
                5. Use the "nothing to lose" angle:
                   - No obligation
                   - Free experience
                   - They choose the time and place
                
                Be bold and confident but maintain a casual, friendly tone.
                Make them feel they'd be crazy to pass up this opportunity.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your ultimate convincing response here",
                  "should_progress": "yes if they show ANY interest, no if still trying to convince"
                }""",
                next_nodes={"default": "final_decision"},
                persistence_condition="interactions>=1"
            ),
            
            # Decision node - Final Decision
            "final_decision": PipelineNode(
                id="final_decision",
                name="Final Decision",
                type=NodeType.DECISION,
                prompt="""IMPORTANT - This is the final decision point that determines if the conversation continues or ends.

Carefully analyze the user's response after our final convincing attempt. Focus EXACTLY on what they said.

Return EXACTLY "yes" if ANY of these apply:
- They show ANY sign of interest whatsoever
- They express willingness to try a test ride or learn more
- They ask any follow-up questions about the bikes
- They respond positively or neutrally without declining
- They say anything that isn't a clear rejection
- They seem even slightly persuaded by the final pitch
- They use ambiguous or uncertain language

Return EXACTLY "no" ONLY if:
- They firmly decline for a second time with language like "no," "not interested"
- They explicitly ask to end the conversation
- They are clearly annoyed or frustrated
- They explicitly state they don't want to continue

In case of ANY ambiguity or if you're unsure about their intent, return "yes".

This decision is CRITICAL - if in doubt, choose to continue the conversation!""",
                next_nodes={"yes": "process_national_id", "no": "end"},
                persistence_condition="always"
            ),
            
            # Action node - Display models
            "display_models": PipelineNode(
                id="display_models",
                name="Display the Models",
                type=NodeType.ACTION,
                prompt="""You are a knowledgeable Spiro electric bike product specialist.
                Present these different bike models from our catalog with brief descriptions of each.
                
                Highlight the key features of each model in a way that makes it stand out.
                Focus especially on range, motor power, and terrain suitability.
                
                Keep your response concise - just 1-2 sentences per bike model.
                Ask which model they might be interested in or if they'd like to know more about any specific model.
                
                IMPORTANT TRANSITION GUIDELINES:
                1. Progress to the next node if ANY of these conditions are met:
                   - They explicitly mention or select a specific model
                   - They ask about pricing for a specific model
                   - They want to proceed with a specific model
                   - They express uncertainty or need help choosing
                   - They ask about features or specifications
                   - They want to compare models
                   - They're still undecided
                
                2. Stay on this node ONLY if:
                   - They haven't responded yet
                   - They're asking general questions about electric bikes
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user goes here",
                  "should_progress": "yes or no"
                }
                
                Remember: If they express any uncertainty or need help choosing, progress to gather requirements.""",
                next_nodes={"default": "model_selected_decision"},
                persistence_condition="pending_user_response"
            ),
            
            # Decision node - Model selected?
            "model_selected_decision": PipelineNode(
                id="model_selected_decision",
                name="Has Model Been Selected?",
                type=NodeType.DECISION,
                prompt="""Determine if the user has selected a specific bike model to focus on.
                Return EXACTLY "yes" if they've mentioned a specific model they're interested in.
                Return EXACTLY "no" if they're still undecided or want more information.
                
                Consider these general patterns:
                - If they express uncertainty, confusion, or need help choosing, return "no"
                - If they ask about features, comparisons, or recommendations, return "no"
                - If they mention specific requirements or preferences without selecting a model, return "no"
                - If they explicitly mention or select a specific model, return "yes"
                
                The goal is to understand if they've made a clear model selection or need more guidance.
                When in doubt, return "no" to gather more information about their needs.""",
                next_nodes={"yes": "price_calculation", "no": "gather_requirements"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Gather requirements
            "gather_requirements": PipelineNode(
                id="gather_requirements",
                name="Gather Requirements and Preferences",
                type=NodeType.ACTION,
                prompt="""You are a friendly and casual Spiro electric bike consultant. 
                Have a natural conversation about their needs and preferences for an electric bike.
                
                While chatting, casually explore:
                - How they plan to use the bike (commuting, leisure, etc.)
                - Their typical travel distance
                - Budget considerations
                - Any specific features they're interested in
                - Their style preferences
                
                Be conversational and engaging - don't make it feel like an interview.
                Share relevant information about our bikes as the conversation flows naturally.
                
                If they show interest in specific models or features, feel free to share details about those.
                The goal is to understand their needs while keeping them engaged and interested.
                
                IMPORTANT TRANSITION GUIDELINES:
                1. Progress to the next node if ANY of these conditions are met:
                   - They've mentioned at least 2-3 key requirements or preferences
                   - They express interest in seeing specific models
                   - They ask about pricing or specific features
                   - They seem ready to explore options
                
                2. Stay on this node if:
                   - They're still actively asking questions about requirements
                   - They haven't shared enough information to make meaningful recommendations
                   - They're still exploring their needs
                
                Remember: It's better to move forward with partial information than to get stuck in a loop.
                We can always gather more details as we discuss specific models.""",
                next_nodes={"default": "analyze_requirements"}
            ),
            
            # Action node - Analyze requirements
            "analyze_requirements": PipelineNode(
                id="analyze_requirements",
                name="Analyze Requirements",
                type=NodeType.ACTION,
                prompt="""You are a thoughtful Spiro bike matcher.
                Based on what you've learned about the customer's needs, analyze their requirements and suggest 1-2 potential models that would be a good fit.
                Mention:
                - Which specific Spiro models would meet their needs
                - Why these models are good matches
                - 2-3 key features that align with their preferences
                
                Then ask if they'd like more information about any of these models.
                
                Refer to these available models: {models_list}""".format(models_list=", ".join(self.bike_models)),
                next_nodes={"default": "model_selected_decision"},
                persistence_condition="interactions>=2"  # Allow time for analysis and discussion
            ),
            
            # Action node - Calculate & Share Pricing
            "price_calculation": PipelineNode(
                id="price_calculation",
                name="Calculate & Share Pricing",
                type=NodeType.ACTION,
                prompt="""You are a knowledgeable Spiro pricing specialist.
                
                SPIRO BIKE PRICING INFORMATION:
                - Commando: KES 142,000
                - EKON450M1: KES 162,500
                - EKON400M2: KES 162,500
                - EKON 450M1 (E66): KES 164,000
                - EKON 400M2 (QBC1): KES 163,500
                - EKON 450M3 (E56): KES 169,000
                - EKON 400M2 (V2): KES 158,000
                - EKON 350M1: KES 149,500
                - EKON 250M1: KES 138,000
                - EKON 450H1 i: KES 156,000
                - EKON 350H: KES 145,000
                - EKON 200H: KES 132,000
                - EKON 450H I: KES 155,000
                - EKON ALPHA: KES 147,000
                
                {pricing_details}
                
                Present the pricing information clearly, including:
                - Base price of the model
                - Additional costs (if any)
                - Total on-road price
                - Any current discounts or offers
                
                IMPORTANT REGARDING LOGBOOK TRANSFER:
                - Mention that there's a logbook transfer fee as an additional cost
                - Do NOT mention the exact amount of the logbook transfer fee (KSH 1,700) unless specifically asked
                - If the customer directly asks about the logbook transfer fee amount, then tell them it's KSH 1,700
                
                After sharing the pricing, ask if they'd like to know about financing options.
                Keep your response focused on pricing information, presented in a helpful, transparent way.
                
                Progress to the next node when they've understood the pricing and are ready to explore financing options.
                Stay on this node if they have questions about pricing or want to explore different configurations.""",
                next_nodes={"default": "emi_decision"}
            ),
            
            # Action node - Offer comparison
            "offer_comparison": PipelineNode(
                id="offer_comparison",
                name="Offer Model Comparison",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro comparison specialist.
                
                {comparison_details}
                
                Present a clear side-by-side comparison of these models, focusing on:
                - Key performance differences
                - Feature highlights
                - Price points
                - Best use cases for each
                
                Make the differences clear to help with decision-making.
                Ask which model seems to best fit their needs after the comparison.
                Keep your comparison balanced and informative.""",
                next_nodes={"default": "model_selected_decision"},
                persistence_condition="interactions>=2"  # Allow time for comparison discussion
            ),
            
            # Decision node - EMI inquiry
            "emi_decision": PipelineNode(
                id="emi_decision",
                name="Wants EMI Information?",
                type=NodeType.DECISION,
                prompt="""Determine if the user is interested in EMI (installment) payment options.
                Return EXACTLY "yes" if they express interest in EMI, financing, loan, or installment payments.
                Return EXACTLY "no" if they explicitly decline or aren't interested in payment plans.
                Base your decision only on their explicit statement about financing interest.""",
                next_nodes={"yes": "provide_emi", "no": "address_sacco_inquiry"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
           
            # Consolidated node - ID Documentation Check
            "id_documentation_check": PipelineNode(
                id="id_documentation_check",
                name="ID Documentation Check",
                type=NodeType.DECISION,
                prompt="""You are a helpful Spiro document specialist.
                
                Ask the user if they have their National ID ready for the application process.
                Explain that providing the National ID document helps speed up the application process.
                
                Make your query conversational and not like a static message.
                
                Return EXACTLY "yes" if they indicate they have the ID document ready.
                Return EXACTLY "no" if they indicate they don't have the ID document ready.""",
                next_nodes={"yes": "address_sacco_inquiry", "no": "convince_for_national_id"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Convince for National ID
            "convince_for_national_id": PipelineNode(
                id="convince_for_national_id",
                name="Convince to Arrange National ID",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro document specialist.
                
                The user has indicated they don't have their National ID document ready.
                
                Your task is to:
                1. Explain the benefits of providing ID documents for the application process
                2. Highlight how these documents enable faster loan processing
                3. Mention how ID verification leads to quicker bike delivery
                4. Reassure them about the security of their information
                5. Gently encourage them to arrange their ID documents
                
                Be persuasive but respectful. Use a conversational tone, not static messaging.
                Be conversational and personable in your explanation.
                
                After explaining, ask if they would be willing to provide their address and SACCO details
                in the meantime while they arrange for their ID.""",
                next_nodes={"default": "address_sacco_inquiry"},
                persistence_condition="interactions>=1"
            ),
            
            # Action node - Address and SACCO inquiry
            "address_sacco_inquiry": PipelineNode(
                id="address_sacco_inquiry",
                name="Address and SACCO Inquiry",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro document specialist.
                
                FIRST CHECK THE USER CONTEXT:
                - Check if "sacco_name" is already filled in the user context
                
                If SACCO name is already provided:
                - Acknowledge that we already have their SACCO information
                - Do not ask for it again
                
                If SACCO name is NOT already provided:
                - Ask for their SACCO name directly
                
                Be conversational and friendly, not using static messaging.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user goes here",
                  "should_progress": "yes if they provided a valid SACCO name, no if they need more convincing or provided an invalid SACCO name",
                  "state_field": {
                    "sacco_name": "SACCO name if mentioned by the user and it's valid"
                  }
                }
                
                Only include state_field values that are actually mentioned in the conversation.""",
                next_nodes={"default": "address_sacco_decision"},
                persistence_condition="interactions>=1"  # Allow time to collect all details
            ),
            
            # Decision node - Address and SACCO details
            "address_sacco_decision": PipelineNode(
                id="address_sacco_decision",
                name="Address and SACCO Details Provided?",
                type=NodeType.DECISION,
                prompt="""Determine if the user has provided a valid SACCO name.
                
                VALID SACCO NAMES (only these are valid - KEEP THIS LIST PRIVATE, DO NOT SHOW TO USER):
                - Dago 1 Boda Sacco
                - Naiwest Sacco
                - Lavington Link Sacco
                - Kallor Transcoop
                - Kitisuru Bodaboda Sacco
                - Sang'anyi Stage Sacco
                - Ngomongo
                - 56 Bodacom
                - Utalii Boda Sacco
                - Ananda Marga
                - Lucky summer
                - Ngomongo Sacco
                - United Boda Transport
                - Kibra Boda Sacco
                - Kibra Jiweke
                - Eminent Transcop
                - Kile boda boda sacco
                
                FIRST CHECK THE USER CONTEXT:
                - Check if "sacco_name" is already filled in the user context and it's in the valid list
                
                Return EXACTLY "yes" if ANY of these conditions are met:
                1. The user context already has a valid SACCO name OR they mentioned a valid SACCO name in this conversation
                2. They have explicitly stated they already provided a valid SACCO name
                
                If they provided a SACCO name that is NOT in the valid list:
                - Return "no" so we can ask them to provide a valid SACCO name
                
                IMPORTANT: Do NOT repeatedly ask for confirmation of the same information. If the user has provided a valid SACCO name, accept it without repeatedly confirming the same information.
                
                Your response MUST be in the following JSON format:
                {
                  "decision": "yes or no",
                  "state_field": {
                    "sacco_name": "SACCO name if mentioned by the user and it's valid"
                  }
                }
                
                Only include state_field values that are actually mentioned in the conversation.
                
                Return "yes" as the decision if they've provided a valid SACCO name.
                Return "no" if they have provided an invalid SACCO name or explicitly declined to provide ANY information.""",
                next_nodes={"yes": "upload_national_id", "no": "share_e_documentation"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Share Spiro & Kinga Joint Campaign
            "spiro_kinga_campaign": PipelineNode(
                id="spiro_kinga_campaign",
                name="Share Spiro & Kinga Campaign",
                type=NodeType.ACTION,
                prompt="""You are an enthusiastic Spiro partnerships specialist.
                
                Share information about the Spiro and Kinga joint campaign to digitalize SACCOs:
                1. Explain how Spiro and Kinga are working together to digitalize SACCO operations
                2. Highlight how this digitalization helps SACCO members get loans more quickly
                3. Mention the special benefits for SACCO members, including faster bike financing
                4. Explain how this can help them get a Spiro bike with streamlined financing
                
                Be enthusiastic and conversational in your explanation, not using static messaging.
                Make the benefits clear and compelling.
                
                After explaining, ask if they'd like to proceed with providing their SACCO details to get started.""",
                next_nodes={"default": "second_address_sacco_inquiry"},
                persistence_condition="interactions>=1"
            ),
            
            # Action node - Second Address and SACCO inquiry attempt
            "second_address_sacco_inquiry": PipelineNode(
                id="second_address_sacco_inquiry",
                name="Second Address and SACCO Inquiry",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro document specialist.
                
                This is a second attempt to collect SACCO information from the user.
                
                FIRST CHECK THE USER CONTEXT:
                - Check if "sacco_name" is already filled in the user context
                
                If SACCO name is already provided:
                - Acknowledge that we already have their SACCO information
                - Do not ask for it again
                
                If SACCO name is NOT already provided:
                - Ask for their SACCO name directly
                
                
                Be conversational and friendly, not using static messaging.
                Emphasize that this basic information will help streamline their application process.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user goes here",
                  "should_progress": "yes if they provided a valid SACCO name, no if they still declined or provided an invalid SACCO name",
                  "state_field": {
                    "sacco_name": "SACCO name if mentioned by the user and it's valid"
                  }
                }
                
                Only include state_field values that are actually mentioned in the conversation.""",
                next_nodes={"default": "second_address_sacco_decision"},
                persistence_condition="interactions>=1"
            ),
            
            # Decision node - Second Address and SACCO check
            "second_address_sacco_decision": PipelineNode(
                id="second_address_sacco_decision",
                name="Second Address and SACCO Check",
                type=NodeType.DECISION,
                prompt="""Determine if the user has provided a valid SACCO name on this second attempt.
                
                VALID SACCO NAMES (only these are valid - KEEP THIS LIST PRIVATE, DO NOT SHOW TO USER):
                - Dago 1 Boda Sacco
                - Naiwest Sacco
                - Lavington Link Sacco
                - Kallor Transcoop
                - Kitisuru Bodaboda Sacco
                - Sang'anyi Stage Sacco
                - Ngomongo
                - 56 Bodacom
                - Utalii Boda Sacco
                - Ananda Marga
                - Lucky summer
                - Ngomongo Sacco
                - United Boda Transport
                - Kibra Boda Sacco
                - Kibra Jiweke
                - Eminent Transcop
                - Kile boda boda sacco
                
                Evaluate if they have provided a valid SACCO name from the internal list above (for your reference only, DO NOT show to user).
                Be lenient with minor spelling variations, but only accept names that are on the valid list.
                
                IMPORTANT: Do NOT repeatedly ask for confirmation of the same information. If the user has provided a valid SACCO name, accept it without repeatedly confirming the same information.
                
                Your response MUST be in the following JSON format:
                {
                  "decision": "yes or no",
                  "state_field": {
                    "sacco_name": "SACCO name if mentioned by the user and it's valid"
                  }
                }
                
                Return "yes" as the decision if they've provided a valid SACCO name.
                Return "no" if they have provided an invalid SACCO name or explicitly refused to provide ANY information at all.""",
                next_nodes={"yes": "upload_national_id", "no": "upload_national_id"},  # Both paths lead to ID upload
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Decision node - Upload National ID
            "upload_national_id": PipelineNode(
                id="upload_national_id",
                name="Upload National ID Document",
                type=NodeType.DECISION,
                prompt="""You are a helpful Spiro document specialist.
                
                YOUR TOP PRIORITY is to check if the user has already uploaded their ID document by examining ONLY these system flags:
                1. FIRST AND MOST IMPORTANT: Check if "image_uploaded" is True in the user context
                2. SECOND: Check if "id_upload_complete" is True in the user context
                3. THIRD: Check if "national_id" field has a value in the user context
                
                CRITICAL INSTRUCTIONS:
                - IGNORE conversation history completely for determining if an ID has been uploaded
                - ONLY use the system flags above to determine upload status
                - If ANY of these flags are TRUE, then the user has successfully uploaded their ID
                - The user might say they will upload, they are trying to upload, or have uploaded, but ONLY the system flags reflect what actually happened
                - User statements about uploading NEVER override the system flags
                - If system flags show image_uploaded=True or id_upload_complete=True, then ID has been uploaded REGARDLESS of what the conversation history says
                
                If ANY of the system flags are True (image_uploaded=True OR id_upload_complete=True OR national_id has a value):
                - Thank them for uploading their ID
                - Acknowledge their information is being processed
                - Ask a simple follow-up question about whether all their information is correct
                - Return "yes" in the decision field, since they have successfully uploaded their ID
                
                If ALL the system flags are False (image_uploaded=False AND id_upload_complete=False AND national_id is empty):
                - DIRECTLY instruct them to upload their National ID document using the upload button below
                - Explain that this is a required step to proceed with their application
                - Return "no" in the decision field
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful message based on the system flags status",
                  "decision": "yes if ANY system flag is True, no if ALL system flags are False"
                }""",
                next_nodes={"yes": "process_national_id", "no": "share_e_documentation"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Process National ID
            "process_national_id": PipelineNode(
                id="process_national_id",
                name="Process National ID",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro document processor. The user has agreed to upload their national ID.
                
                FIRST CHECK THE CURRENT STATE:
                1. Check if "image_uploaded" is True in the user context
                2. Check if "id_upload_complete" is True in the user context
                3. Check if the user already has extracted information like first_name, last_name, or national_id
                
                IMPORTANT GUIDANCE FOR HANDLING USER RESPONSES:
                - Always briefly acknowledge what the user just said before continuing
                - If the user says they "will check", "will upload", or "are uploading", acknowledge this positively but STILL check the system flags
                - Phrases like "I'll check", "give me a moment", "let me upload it now" do NOT mean they have completed the upload
                - Only the system flags can confirm if a document was actually uploaded
                - Be conversational but clear about what they need to do next
                
                VALID SACCO NAMES (only these are valid - KEEP THIS LIST PRIVATE, DO NOT SHOW TO USER):
                - Dago 1 Boda Sacco
                - Naiwest Sacco
                - Lavington Link Sacco
                - Kallor Transcoop
                - Kitisuru Bodaboda Sacco
                - Sang'anyi Stage Sacco
                - Ngomongo
                - 56 Bodacom
                - Utalii Boda Sacco
                - Ananda Marga
                - Lucky summer
                - Ngomongo Sacco
                - United Boda Transport
                - Kibra Boda Sacco
                - Kibra Jiweke
                - Eminent Transcop
                - Kile boda boda sacco
                
                If the user HAS ALREADY UPLOADED their ID (any of the above flags are true):
                1. Thank them for uploading their ID
                2. Confirm the information we've extracted (mention their name if we have it)
                3. Check if any required information is still missing:
                   - Simple address (city is sufficient)
                   - SACCO name (if applicable)
                4. If SACCO name is missing, ask them to provide their SACCO name without showing the list
                5. If they provide a SACCO name that is NOT on the list, politely inform them it's not valid and ask again
                6. When all information is collected, indicate we're ready to proceed

                If they HAVEN'T uploaded their ID yet:
                1. Acknowledge their previous message briefly
                2. Naturally guide them to click the UPLOAD button below the chat
                3. Mention they should select their National ID image file
                4. Let them know our system will extract their information automatically
                5. Gently remind them we need this to continue the process
                
                The following information must be collected either from the ID or provided manually:
                - Full name (first and last name)
                - National ID number
                - Simple address (city is sufficient)
                - SACCO name (if applicable)
                
                IMPORTANT: Do NOT insist on complete address details like building, street, state, and zip code.
                A simple city and SACCO name is all that's required to proceed.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user",
                  "should_progress": "yes if all required info is collected, no if still collecting",
                  "state_field": {
                    "first_name": "First name if mentioned or extracted",
                    "last_name": "Last name if mentioned or extracted",
                    "city": "City if mentioned or extracted",
                    "sacco_name": "SACCO name if mentioned and it's valid"
                  }
                }
                
                Only include state_field values that are actually present in the conversation.""",
                next_nodes={"default": "confirm_booking"},
                persistence_condition="image_uploaded && id_upload_complete && first_name != null && national_id != null"  # Only transition when ID is actually uploaded, processed and minimum info collected
            ),
            
            # Action node - Collect Lead Information
            "collect_lead_info": PipelineNode(
                id="collect_lead_info",
                name="Collect Lead Information",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro customer information specialist.
                
                The user has uploaded their ID document, and we've extracted some information. Now, we need to:
                
                1. Confirm the information we extracted is correct
                2. Fill in any missing information required for their application
                
                IMPORTANT GUIDANCE FOR HANDLING USER RESPONSES:
                - Always briefly acknowledge what the user just said before continuing
                - Be conversational and interactive, responding directly to their most recent message
                - If they've provided any information in their last message, acknowledge it specifically
                - Keep the conversation flowing naturally while collecting the required information
                
                VALID SACCO NAMES (only these are valid - KEEP THIS LIST PRIVATE, DO NOT SHOW TO USER):
                - Dago 1 Boda Sacco
                - Naiwest Sacco
                - Lavington Link Sacco
                - Kallor Transcoop
                - Kitisuru Bodaboda Sacco
                - Sang'anyi Stage Sacco
                - Ngomongo
                - 56 Bodacom
                - Utalii Boda Sacco
                - Ananda Marga
                - Lucky summer
                - Ngomongo Sacco
                - United Boda Transport
                - Kibra Boda Sacco
                - Kibra Jiweke
                - Eminent Transcop
                - Kile boda boda sacco
                
                Be conversational and friendly in your approach. If we already have their name from the ID, address them by name.
                
                Ask for any missing information one at a time in a natural conversation flow:
                - Confirm their full name if needed
                - Confirm their ID number if needed
                - Ask for their city/location if not already provided
                - Ask if they're a member of any SACCO (Savings and Credit Cooperative)
                
                If they provide a SACCO name that is NOT on the valid list:
                - Politely inform them that we don't have that SACCO in our system
                - Ask them to provide one from the valid SACCO list above
                - Only accept SACCO names that match those in the valid list (allowing for minor spelling variations)
                - Do not progress until they provide a valid SACCO name
                
                IMPORTANT: Do NOT request detailed address information like specific building, street, etc.
                A simple city name is sufficient for their location.
                
                Progress to the next node once:
                1. We have confirmed their name, ID, and city
                2. They've provided a valid SACCO name from the list

                Your response MUST be in the following JSON format:
                {
                  "response": "Your helpful response to the user",
                  "should_progress": "yes if we have all necessary info, no if still collecting",
                  "state_field": {
                    "first_name": "First name if confirmed or provided",
                    "last_name": "Last name if confirmed or provided",
                    "national_id": "National ID if confirmed or provided",
                    "city": "City if confirmed or provided",
                    "sacco_name": "SACCO name if confirmed or provided and it's valid"
                  }
                }
                
                Only include state_field values that are actually confirmed or provided in the conversation.""",
                next_nodes={"default": "confirm_booking"},
                persistence_condition="interactions>=1 && first_name != null && national_id != null"  # Allow transition only once we have minimum data
            ),
            
            # Action node - Share e-documentation benefits
            "share_e_documentation": PipelineNode(
                id="share_e_documentation",
                name="Share Benefits of e-Documentation",
                type=NodeType.ACTION,
                prompt="""You are an enthusiastic Spiro e-documentation specialist.

                First, briefly explain the benefits of e-documentation:
                - Eliminates need for physical paperwork
                - Faster processing and approval times
                - Secure digital storage of information
                - Easier tracking of application status
                - Environmental benefits of going paperless
                
                Then, SPECIFICALLY and CLEARLY ask the user to UPLOAD their National ID document now:
                - Directly instruct them to click the upload button below the chat
                - Explicitly mention they need to select their National ID document
                - Emphasize this is a required step to proceed with their application
                - DO NOT ask if they are ready or willing to upload - directly instruct them to upload now
                - Mention that the system will automatically extract information to speed up the process
                
                Your message MUST include a clear instruction like: "Please upload your National ID document now using the upload button below."
                
                DO NOT ask questions like "Would you be willing to upload?" or "Are you ready to upload?" - instead, directly guide them to upload.
                Reassure them about the security of the digital process while instructing them to upload now.""",
                next_nodes={"default": "upload_national_id"}# Allow for at least one interaction
            ),

            # Action node - Provide EMI options
            "provide_emi": PipelineNode(
                id="provide_emi",
                name="Provide EMI Information",
                type=NodeType.ACTION,
                prompt="""You are a knowledgeable Spiro finance specialist.
                
                SPIRO FINANCING SCHEME:
                - 10% deposit required (downpayment)
                - Once loan is approved, customer pays 90% balance 
                - Maximum tenure: 24 months
                - Available tenure option: 24 months
                - Insurance is included in the financing
                
                {emi_options}
                
                Present EMI (installment) payment options clearly, including:
                - Available duration plan (24 months only)
                - Monthly payment amounts
                - Down payment requirements (10% of bike price)
                - Total cost breakdown
                
                
                DO NOT mention or ask for National ID in this node.
                Ask if they'd like to proceed with a specific EMI plan or have questions.
                Keep your response organized and easy to understand.
                Dont ask them for deposit or anything , just stick to the instructions and only do the needfull, if they ask anything apart from the information that you have , kindly tell them to get that info when they visit the showroom , which will be shared in furhter part of the conversation.
                Progress to the next node when they've selected an EMI plan.
                Stay on this node if they have questions about financing options or need clarification.""",
                next_nodes={"default": "address_sacco_inquiry"}
            ),

            # Action node - Scheduling
            "scheduling": PipelineNode(
                id="scheduling",
                name="Get Schedule Date & Time",
                type=NodeType.ACTION,
                prompt="""You are a friendly Spiro appointment coordinator.

Ask when would be a convenient date and time for them to:
- Visit the selected showroom
- Complete any remaining paperwork
- Finalize their purchase

Refer to their selected showroom by name if available.

IMPORTANT: All Spiro showrooms operate Monday to Friday from 9 AM to 6 PM. You MUST mention these operating hours explicitly in your response and ensure any suggested times fall within these hours.

Suggest some general time slots if they seem unsure, always making sure they are within operating hours (Mon-Fri, 9 AM-6 PM).
Keep your question simple and focused on getting scheduling information.
Be flexible and accommodating in your approach.

When the user mentions a relative date like "tomorrow" or "next Saturday", respond with the actual calendar date to confirm. If they suggest a weekend date, politely remind them that showrooms are only open Monday to Friday.
Be aware of the time of day and the time of the year when responding to the user.
if the user mentions a time , check if the time is within the operating hours of the showroom.And also if you think that he mentions today if that time has already passed before responding to the user , if the time has already passed suggest a different time that is within the operating hours of the showroom.
If the user context has a reference_id, you MUST mention that reference ID in your response as their booking reference number. For example, "Your booking reference number is [reference_id]".

Your response MUST be in the following JSON format:
{
  "response": "Your scheduling response here",
  "should_progress": "yes if they've provided a time, no if still discussing", 
  "state_field": {"scheduled_time": "The time provided by the user if they've selected a time or null"}
}""",
                next_nodes={"default": "ask_feedback"},
                persistence_condition="interactions>=1"  # Allow scheduling discussion
            ),
            
            # Action node - Share showroom details
            "share_showroom": PipelineNode(
                id="share_showroom",
                name="Share Details With Nearest Showroom",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro location specialist.
                
                SPIRO SHOWROOM LOCATIONS:
                
                COAST REGION:
                - Likoni Motors (Likoni) - Non-exclusive - Coordinates: -4.0921641,39.6309121
                - Likoni Motors (Ukunda) - Non-exclusive - Coordinates: -4.309302,39.5528753
                - Tishel Motors (Leisure) - Non-exclusive - Coordinates: -4.0505763,39.6645883
                - Pikipiki Limited (Ganjoni) - Non-exclusive - Coordinates: -4.0594844,39.6550834
                - Aaliqadar Malindi - Exclusive - Coordinates: -4.0516399,39.6502434
                - Aaliqadar Kilifi - Exclusive - Coordinates: -3.6190589,39.8321398
                - Nguvu Motors (Mtwapa) - Non-exclusive - Coordinates: -3.9540554,39.7426047
                - Kibaomotors Mtwapa - Exclusive - Coordinates: -3.9398528,39.7256772
                - Kibaomotors Mariakani - Exclusive - Coordinates: -3.8656674,39.4635343
                - Kibaomotors Changamwe - Exclusive - Coordinates: -4.0215102,39.6306397
                
                NAIROBI REGION:
                - Masters (Ruaka) - Non-exclusive - Coordinates: -1.2071341,36.7532007
                - CN MOTORS (Githurai 45) - Non-exclusive - Coordinates: -1.2068917,36.918061
                - Kibaomotors Fedha - Exclusive - Coordinates: -1.3116841,36.890151
                - Kibaomotors Kayole Junction - Exclusive - Coordinates: -1.2781171,36.9020543
                - Global Movers (Westlands) - Exclusive - Coordinates: -1.2644643,36.8029808
                - Letland Investments (Kitengela) - Exclusive - Coordinates: -1.5167072,36.8474211
                
                KISUMU REGION:
                - Agatsya Kisumu - Exclusive - Coordinates: 0°06'02.5"S 34°45'07.6"E
                
                KISII REGION:
                - Agatsya Kisii - Exclusive - Coordinates: -0.6803741,34.7569481
                
                KAKAMEGA REGION:
                - Agatsya Kakamega - Exclusive - Coordinates: 0.2818605,34.7255268
                
                ELDORET REGION:
                - Meena Collection Eldoret - Non-exclusive - Coordinates: 0.5192899,35.2677564
                - Kossons Kapsabet - Exclusive - Coordinates: 0.2018537,35.0970594
                
                OPERATING HOURS FOR ALL SHOWROOMS:
                Monday to Friday: 9 AM to 6 PM
                Closed on weekends
                
                Share details about the 2-3 nearest showroom locations to the customer based on their location:
                - For each showroom (maximum 3 locations), provide:
                  - The full address of the showroom
                  - Operating hours (Monday to Friday, 9 AM to 6 PM)
                  - Contact information
                  - Whether it's exclusive or non-exclusive
                
                IMPORTANT: You MUST explicitly mention that all showrooms are open Monday to Friday from 9 AM to 6 PM.
                
                If the customer mentions a specific region or city, prioritize showing showrooms from that area.
                If the customer doesn't specify a location preference, choose showrooms from the most populated regions.
                
                Ask the customer which of these locations they prefer to visit.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your showroom information here followed by a question about which location they prefer",
                  "should_progress": "yes if they've chosen a location, no if still discussing", 
                  "state_field": {"selected_showroom": "The location name if selected by the user or null"}
                }""",
                next_nodes={"default": "scheduling"},
                persistence_condition="interactions>=1"  # Allow minimal interaction for showroom info
            ),
            
            # Action node - Ask for feedback
            "ask_feedback": PipelineNode(
                id="ask_feedback",
                name="Ask Feedback About Overall Experience",
                type=NodeType.ACTION,
                prompt="""You are a thoughtful Spiro customer experience specialist.

                IMPORTANT: You MUST explicitly ask the user for their feedback on the sales process.
                
                Your message should:
                1. Thank them for their time and interest
                2. If a reference_id is available in the user context, remind them of their appointment and reference number: "Your appointment has been scheduled and your reference number is [reference_id]"
                3. Mention that their feedback is highly valuable for improving our service
                4. Directly ask them what they thought about the sales experience
                5. Ask what they liked and what could be improved
                6. End with a warm, appreciative tone
                
                Be conversational, friendly, and make it clear you're specifically requesting their feedback.
                DO NOT assume they've already provided feedback or skip asking for it.""",
                next_nodes={"default": "end"},
                persistence_condition="interactions>=1"  # Allow faster transition to end node
            ),
            
            # Action node - Provide feature information
            "provide_feature_info": PipelineNode(
                id="provide_feature_info",
                name="Provide Feature Information",
                type=NodeType.ACTION,
                prompt="""You are a knowledgeable Spiro electric bike specialist.
                
                {feature_details}
                
                Present this information in a helpful, educational way.
                Explain technical terms simply where needed.
                
                After providing the information, ask if they'd like to know more about other features
                or if they're ready to see the pricing for a specific model.
                
                Keep your response focused and informative.""",
                next_nodes={"default": "model_selected_decision"},
                persistence_condition="interactions>=2"  # Allow feature discussion
            ),
            
            # End node
            "end": PipelineNode(
                id="end",
                name="End Conversation",
                type=NodeType.ACTION,
                prompt="""You are a professional Spiro representative concluding the conversation.
                Thank the user for their time and:
                1. Express appreciation for their interest in learning about EVs
                2. Let them know they can reach out anytime if they change their mind
                3. Provide contact information for future reference
                4. Wish them well
                
                Keep the response polite and professional, leaving the door open for future interaction.""",
                next_nodes={},  # No next nodes since this is an end node
                persistence_condition="always"
            ),
            
            # Action node - Confirm Booking
            "confirm_booking": PipelineNode(
                id="confirm_booking",
                name="Confirm Booking",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro booking specialist.
                
                Summarize the information collected from the user so far, including:
                1. Their selected bike model (if applicable)
                2. Any financing preferences (if applicable)
                3. Their personal details (name from ID)
                
                Thank them for providing their information and explain the next steps:
                - We will now show them a list of nearby showrooms based on their location
                - They can select which showroom they'd prefer to visit
                - After selection, they'll be asked for a convenient date and time
                
                Be conversational, friendly, and genuinely appreciative of their time.""",
                next_nodes={"default": "list_showrooms"},
                persistence_condition="always"
            ),
            
            # Action node - List Showrooms
            "list_showrooms": PipelineNode(
                id="list_showrooms",
                name="List Available Showrooms",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro location specialist.
                
                SPIRO SHOWROOM LOCATIONS:
                
                COAST REGION:
                - Likoni Motors (Likoni) - Non-exclusive - Coordinates: -4.0921641,39.6309121
                - Likoni Motors (Ukunda) - Non-exclusive - Coordinates: -4.309302,39.5528753
                - Tishel Motors (Leisure) - Non-exclusive - Coordinates: -4.0505763,39.6645883
                - Pikipiki Limited (Ganjoni) - Non-exclusive - Coordinates: -4.0594844,39.6550834
                - Aaliqadar Malindi - Exclusive - Coordinates: -4.0516399,39.6502434
                - Aaliqadar Kilifi - Exclusive - Coordinates: -3.6190589,39.8321398
                - Nguvu Motors (Mtwapa) - Non-exclusive - Coordinates: -3.9540554,39.7426047
                - Kibaomotors Mtwapa - Exclusive - Coordinates: -3.9398528,39.7256772
                - Kibaomotors Mariakani - Exclusive - Coordinates: -3.8656674,39.4635343
                - Kibaomotors Changamwe - Exclusive - Coordinates: -4.0215102,39.6306397
                
                NAIROBI REGION:
                - Masters (Ruaka) - Non-exclusive - Coordinates: -1.2071341,36.7532007
                - CN MOTORS (Githurai 45) - Non-exclusive - Coordinates: -1.2068917,36.918061
                - Kibaomotors Fedha - Exclusive - Coordinates: -1.3116841,36.890151
                - Kibaomotors Kayole Junction - Exclusive - Coordinates: -1.2781171,36.9020543
                - Global Movers (Westlands) - Exclusive - Coordinates: -1.2644643,36.8029808
                - Letland Investments (Kitengela) - Exclusive - Coordinates: -1.5167072,36.8474211
                
                KISUMU REGION:
                - Agatsya Kisumu - Exclusive - Coordinates: 0°06'02.5"S 34°45'07.6"E
                
                KISII REGION:
                - Agatsya Kisii - Exclusive - Coordinates: -0.6803741,34.7569481
                
                KAKAMEGA REGION:
                - Agatsya Kakamega - Exclusive - Coordinates: 0.2818605,34.7255268
                
                ELDORET REGION:
                - Meena Collection Eldoret - Non-exclusive - Coordinates: 0.5192899,35.2677564
                - Kossons Kapsabet - Exclusive - Coordinates: 0.2018537,35.0970594
                
                OPERATING HOURS FOR ALL SHOWROOMS:
                Monday to Friday: 9 AM to 6 PM
                Closed on weekends
                
                Share details about the 2-3 nearest showroom locations to the customer based on their location:
                - For each showroom (maximum 3 locations), provide:
                  - The full address of the showroom
                  - Operating hours (Monday to Friday, 9 AM to 6 PM)
                  - Contact information
                  - Whether it's exclusive or non-exclusive
                
                IMPORTANT: You MUST explicitly mention that all showrooms are open Monday to Friday from 9 AM to 6 PM.
                
                If the customer mentions a specific region or city, prioritize showing showrooms from that area.
                If the customer doesn't specify a location preference, choose showrooms from the most populated regions.
                
                Ask the customer which of these locations they prefer to visit.
                
                Your response MUST be in the following JSON format:
                {
                  "response": "Your showroom information here followed by a question about which location they prefer",
                  "should_progress": "yes if they've chosen a location, no if still discussing", 
                  "state_field": {"selected_showroom": "The location name if selected by the user or null"}
                }""",
                next_nodes={"default": "showroom_selection_decision"},
                persistence_condition="interactions>=1"
            ),
            
            # Add a decision node to check if user has selected a showroom
            "showroom_selection_decision": PipelineNode(
                id="showroom_selection_decision",
                name="Check Showroom Selection",
                type=NodeType.DECISION,
                prompt="""Determine if the user has selected a specific showroom from the list provided.
                
                Check if the user's response contains the name of ANY showroom from our locations list.
                Be flexible with partial matches and variations in how they might refer to the showroom.
                
                Examples of showroom names:
                - Likoni Motors
                - Tishel Motors
                - Aaliqadar Malindi
                - Kibaomotors Fedha
                - Global Movers
                - Agatsya Kisumu
                - Meena Collection Eldoret
                - Any other showroom in our network
                
                If they've clearly selected a showroom, return EXACTLY "yes" and capture the selection.
                If they haven't made a clear selection, return EXACTLY "no".
                
                Your response MUST be in the following JSON format:
                {
                  "decision": "yes or no",
                  "reasoning": "Brief explanation of your decision",
                  "state_field": {"selected_showroom": "The exact showroom name selected by the user or null if none selected"}
                }""",
                next_nodes={"yes": "scheduling", "no": "list_showrooms"},
                persistence_condition="always"
            ),
            
            # Action node - ID Information Decision
            "id_information_decision": PipelineNode(
                id="id_information_decision",
                name="ID Information Check",
                type=NodeType.DECISION,
                prompt="""Determine if the user's ID document has all the required information.
                If the ID has all required information, return EXACTLY "yes". 
                If information is missing, return EXACTLY "no".
                
                Check for:
                - Full name
                - ID number
                - Any address information
                
                Your decision should be based solely on whether the ID document has provided sufficient information to proceed.""",
                next_nodes={"yes": "collect_sacco_details", "no": "collect_missing_info"},
                persistence_condition="always"  # Decision nodes always transition
            ),
            
            # Action node - Collect Missing Information
            "collect_missing_info": PipelineNode(
                id="collect_missing_info",
                name="Collect Missing Information",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro customer information specialist.
                
                We've identified that some essential information is missing from the ID document or our records.
                
                Ask for any missing information in a conversational, friendly way:
                - If we don't have their full name, ask for it
                - If we don't have their ID number, ask for it
                - If we don't have their location, ask for their city (only basic location needed)
                
                IMPORTANT: Only request information that is actually missing.
                Do NOT ask for detailed address information - a simple city name is sufficient.
                
                After collecting the required information, thank them and explain that now we'll proceed to ask about SACCO details.""",
                next_nodes={"default": "collect_sacco_details"},
                persistence_condition="interactions>=1"  # Allow at least one interaction to collect information
            ),
            
            # Action node - Collect SACCO Details
            "collect_sacco_details": PipelineNode(
                id="collect_sacco_details",
                name="Collect SACCO Details",
                type=NodeType.ACTION,
                prompt="""You are a helpful Spiro finance specialist.
                
                Ask for any SACCO membership details the user may have:
                - SACCO name (if they're a member of any SACCO)
                - How long they've been a SACCO member
                
                Explain that this information can help with:
                - Potentially faster loan processing
                - Special offers for SACCO members
                
                Emphasize that SACCO membership is not required.
                
                Be conversational and friendly, not using static messaging.""",
                next_nodes={"default": "confirm_booking"},  # Changed from share_showroom to confirm_booking
                persistence_condition="interactions>=1"  # Allow time to collect SACCO details
            ),
        }
    
    def _calculate_price(self, model: str) -> Dict[str, Any]:
        """Calculate price details for a model based on specifications"""
        specs = self.bike_data.get("BIKE_SPECS", {}).get(model, {})
        
        # Specific model pricing data
        model_prices = {
            "Commando": 142000,
            "EKON450M1": 162500,
            "EKON400M2": 162500,
            "EKON 450M1 (E66)": 164000,
            "EKON 400M2 (QBC1)": 163500,
            "EKON 450M3 (E56)": 169000,
            "EKON 400M2 (V2)": 158000,
            "EKON 350M1": 149500,
            "EKON 250M1": 138000,
            "EKON 450H1 i": 156000,
            "EKON 350H": 145000,
            "EKON 200H": 132000,
            "EKON 450H I": 155000,
            "EKON ALPHA": 147000
        }
        
        # Check if we have specific pricing for this model
        if model in model_prices:
            base_price = model_prices[model]
        else:
            # Base price calculation based on motor power and battery capacity
            base_price = 75000  # Default base price
            
            # Adjust for motor power
            motor_power = specs.get("MOTOR_POWER", "")
            if isinstance(motor_power, str):
                if "9.4" in motor_power:
                    base_price += 25000
                elif "9" in motor_power:
                    base_price += 20000
                elif "7.5" in motor_power:
                    base_price += 15000
                elif "4.5" in motor_power:
                    base_price += 10000
            
            # Adjust for battery capacity
            battery_capacity = specs.get("BATTERY_CAPACITY", [])
            if isinstance(battery_capacity, list) and battery_capacity:
                capacity = battery_capacity[0]
                if "58" in capacity:
                    base_price += 20000
                elif "46" in capacity:
                    base_price += 15000
                elif "45" in capacity:
                    base_price += 12000
                elif "38" in capacity:
                    base_price += 8000
            
            # Adjust for range
            range_spec = specs.get("RANGE", "")
            if isinstance(range_spec, str):
                if "180" in range_spec:
                    base_price += 15000
        
        # Calculate discounts
        discount_percentage = 5  # 5% discount
        discount_amount = base_price * (discount_percentage / 100)
        
        # Calculate final price
        final_price = base_price - discount_amount
        
        # Optional accessories
        accessories = [
            {"name": "Premium Seat", "price": 2500},
            {"name": "Rear Carrier", "price": 1800},
            {"name": "Mobile Holder", "price": 800},
            {"name": "Helmet", "price": 1500}
        ]
        
        return {
            "model": model,
            "base_price": base_price,
            "discount_percentage": discount_percentage,
            "discount_amount": discount_amount,
            "final_price": final_price,
            "accessories": accessories
        }
    
    def _calculate_emi_options(self, price: float) -> List[Dict[str, Any]]:
        """Calculate EMI options for a given price"""
        options = []
        
        # Common parameters
        down_payment_percentage = 0.10  # 10% down payment
        down_payment = price * down_payment_percentage
        remaining_amount = price - down_payment
        
        # 18-month EMI option
        monthly_18m = remaining_amount / 18
        options.append({
            "duration": 18,
            "down_payment": down_payment,
            "monthly_payment": monthly_18m,
            "interest_rate": 0,  # No interest mentioned
            "total_cost": price,
            "insurance": "Included"
        })
        
        # 24-month EMI option
        monthly_24m = remaining_amount / 24
        options.append({
            "duration": 24,
            "down_payment": down_payment,
            "monthly_payment": monthly_24m,
            "interest_rate": 0,  # No interest mentioned
            "total_cost": price,
            "insurance": "Included"
        })
        
        return options
    
    def _call_llm(self, node: PipelineNode, user_input: str) -> str:
        """Call LLM with the appropriate prompt based on node type"""
        start_time = time.time()
        current_model = MODEL
        
        # Prepare context information for the prompt
        context_info = self._prepare_context_information()
        conversation_history = self._prepare_conversation_history()
        
        # Get information about the current node
        node_info = f"You are in the '{node.name}' step of the conversation."
        
        # Determine how many times the user has interacted with this node
        interactions = self.context.get_node_interactions(node.id)
        node_interactions = f"This is interaction #{interactions + 1} with this node."
        
        # Track this interaction with the node
        self.context.increment_node_interaction(node.id)
        
        # Get current date and time information
        current_datetime = datetime.datetime.now()
        current_date = current_datetime.strftime("%A, %B %d, %Y")
        current_day_of_week = current_datetime.strftime("%A")
        current_time = current_datetime.strftime("%I:%M %p")
        
        # Use the node prompt without formatting
        formatted_prompt = node.prompt
        
        # Common header for all prompts
        common_header = f"""
        RESPONSE FORMAT INSTRUCTIONS:
        - For all nodes, return a JSON response with the following fields:
          - response: Your conversational response to the user
          - should_progress: "yes" if we should proceed to the next node, or "no" if more interaction is needed
          - state_field: If you identify a specific state that should be updated (like selected_model), include it here
        - The JSON should follow this structure: {{"response": "Your message", "should_progress": "yes/no", "state_field": {{"field_name": "value"}}}}
        - For model selection, if a user has clearly selected a model, include "selected_model" in the state_field with the model name
        - IMPORTANT: DO NOT wrap your JSON in markdown code blocks (``` or ```json). Return the raw JSON object directly.
        - IMPORTANT: Your entire response must be ONLY a valid JSON object, without any additional text before or after.
        - IMPORTANT: Do NOT include any explanations or comments outside the JSON object.
                
        RESPONSE STRUCTURE GUIDELINES:
        - ALWAYS briefly acknowledge the user's most recent message before continuing the conversation
        - Be conversational and interactive - respond directly to what the user just said
        - Keep your responses natural and friendly, as if texting with a person
        - Use proper spacing with new lines to separate paragraphs
        - Use simple hyphens for listing items or options
        - Format phone numbers, prices, and other numerical information consistently
        - For emphasis, use simple techniques like ALL CAPS for very important points
        - When presenting options, number them (1., 2., 3.) for clarity
        - CRITICAL: ALWAYS end your response with a question or call-to-action that requires the user to respond
        - NEVER end with just a statement or information without giving the user a clear reason to respond
        - If providing information, follow it with a question like "What do you think?" or "Would you like to know more?"
        - Dont ask the user what do you want to do next since the flow is already decided , just ask rhetorical questions . 
        - Even when concluding a topic, ask what they'd like to do next or how they feel about the information
        
        CONVERSATION GUIDELINES:
        - Stay focused on your role and the current conversation node
        - Do not provide help or information unrelated to selling Spiro electric bikes
        - If the user asks for unrelated assistance, politely redirect them back to the bike discussion
        - Remember your purpose is to guide the customer through the sales process
        - IMPORTANT: For document uploads and ID verification, ONLY rely on the system state flags (image_uploaded, id_upload_complete)
        - If a user says they are checking or will upload their document, acknowledge this but wait for actual system confirmation
        - If a user claims to have uploaded a document, but system flags show they haven't, gently guide them to actually complete the upload
        - IMPORTANT: Never make up any information. Only use the bike model information provided below and the context information. If you don't know something, say so.
        - Keep messages brief and to the point - avoid long explanations
        - IMPORTANT: Ensure EVERY response is interactive and provides a clear way for the user to continue the conversation
        
        CRITICAL CONTEXT AWARENESS GUIDELINES:
        - ALWAYS carefully read the conversation history before responding
        - Pay close attention to what questions were asked and how the user responded
        - Never confuse a response to one question with a response to a different question
        - When the user says "no", make sure you understand exactly what they're saying no to
        - Each node in the conversation deals with a different topic - be aware of the current topic
        - Be especially careful with test ride responses vs. document provision responses
        - Remember that declining a test ride does NOT mean declining to provide documentation
        - Separate concerns: test rides, documentation, financing are all distinct topics
        - Do not jump to conclusions about what the user means - read their exact words in context
        
        SHOWROOM INFORMATION:
        - All Spiro showrooms are open Monday to Friday, 9 AM to 6 PM
        - When discussing showroom visits, test rides, or scheduling appointments, ALWAYS mention these operating hours
        - Ensure any scheduled times fall within these operating hours
        - For showroom-related nodes (show_showrooms), emphasize these hours
        
        CURRENT DATE AND TIME CONTEXT:
        Today is {current_date}, which is {current_day_of_week}.
        The current time is {current_time}.
        Be aware of the time of day and the time of the year when responding to the user.
        BIKE MODEL INFORMATION:
        {self._generate_bike_details(11)}
        """
        if node.id == "end":
            common_header += """
            CONTACT INFORMATION:
            - Phone: 254 116 931615
            - Website: https://www.spironet.com

            IMPORTANT: Include this contact information in your response.
            """

        # Prepare system message based on node type
        if node.type == NodeType.ACTION:
            # For Action nodes, use a detailed system message with function calling capability
            system_content = f"""
            {common_header}
            
            {formatted_prompt}
            
            {node_info}
            {node_interactions}
            
            CONTEXT INFORMATION:
            {context_info}
            
            CONVERSATION HISTORY:
            {conversation_history}
            
            STATE DETECTION GUIDANCE:
            - If the user selects a model (such as EKON ALPHA, EKON 450M1, etc), include that in the state_field as "selected_model"
            - If the user mentions a city or location, include that in the state_field as "city"
            - If the user mentions a SACCO name, include that in the state_field as "sacco_name"
            - If the user provides information about their usage (commuting, leisure, etc), include that in the state_field as "usage_type"
            - If the user mentions a specific terrain or distance, include that in the state_field
            - Don't create a separate LLM call just to extract this information - include it within your response
            
            RESPONSE FORMAT:
            You must respond with a JSON object containing:
            - 'response': Your actual conversational response to the user
            - 'should_progress': 'yes' if we should proceed to the next node, or 'no' if more interaction is needed
            - 'state_field': Include any detected states here, especially 'selected_model' if a model was selected
            
            Example: {{"response": "Great choice! The EKON ALPHA is perfect for your needs.", "should_progress": "yes", "state_field": {{"selected_model": "EKON ALPHA"}}}}
            """
            
            # Define function calling capabilities
            functions = [
                {
                    "name": "get_bike_model_info",
                    "description": "Get detailed specifications for a specific bike model or compare multiple models",
                    "parameters": {
                        "type": "object", 
                        "properties": {
                            "model_name": {
                                "type": "string", 
                                "description": "Name of the bike model to get information about"
                            },
                            "comparison_models": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of models to compare (include model_name as first item if comparing)"
                            },
                            "specific_feature": {
                                "type": "string",
                                "description": "Specific feature to query about (e.g., RANGE, MOTOR_POWER, BATTERY_CAPACITY)"
                            }
                        },
                        "required": ["model_name"]
                    }
                }
            ]
            
            messages = [
                {"role": "system", "content": system_content},
                {"role": "user", "content": user_input}
            ]
        elif node.type == NodeType.DECISION:
            # For Decision nodes, system message emphasizes extracting a concrete decision
            system_content = f"""
            {common_header}
            
            {formatted_prompt}
            
            {node_info}
            
            CONTEXT INFORMATION:
            {context_info}
            
            CONVERSATION HISTORY:
            {conversation_history}
            
            Your job is to extract the decision from the user's input.
            Return a JSON response with the decision as well.
            If detecting model selection, include the selected model in the state_field.
            
            RESPONSE FORMAT:
            You must respond with a JSON object containing:
            - 'response': One of the exact options specified above (e.g., "yes" or "no")
            - 'should_progress': Always "yes" for decision nodes
            - 'state_field': Include any detected states here, especially 'selected_model' if a model was selected
            
            Example: {{"response": "yes", "should_progress": "yes", "state_field": {{"selected_model": "EKON ALPHA"}}}}
            """
            
            messages = [
                {"role": "system", "content": system_content},
                {"role": "user", "content": user_input}
            ]
            
            # Decision nodes don't need function calling
            functions = None
        else:
            # For START_END nodes, simple system message
            system_content = f"""
            {common_header}
            
            {formatted_prompt}
            
            {node_info}
            
            CONTEXT INFORMATION:
            {context_info}
            
            CONVERSATION HISTORY:
            {conversation_history}
            
            RESPONSE FORMAT:
            You must respond with a JSON object containing:
            - 'response': Your actual conversational response to the user
            - 'should_progress': 'yes' if we should proceed to the next node, or 'no' if more interaction is needed
            - 'state_field': Include any detected states here
            
            Example: {{"response": "Thank you for your interest in Spiro bikes!", "should_progress": "yes", "state_field": {{}}}}
            """
            
            messages = [
                {"role": "system", "content": system_content},
                {"role": "user", "content": user_input}
            ]
            
            # START_END nodes don't need function calling
            functions = None
        
        # Make API call with retry logic to fallback model
        try:
            # First try with primary model
            response = self._make_api_call(messages, node, functions, current_model, user_input, system_content, start_time)
            return response
        except Exception as primary_error:
            logger.error(f"Error with primary model {MODEL}: {str(primary_error)}")
            
            try:
                # Try fallback model if primary fails
                logger.info(f"Attempting fallback to {FALLBACK_MODEL} for this request only")
                fallback_response = self._make_api_call(messages, node, functions, FALLBACK_MODEL, user_input, system_content, start_time)
                
                # Add note about fallback model usage to the call record
                self.llm_calls[-1].model = FALLBACK_MODEL if self.llm_calls else None
                
                logger.info(f"Successfully used fallback model {FALLBACK_MODEL}")
                return fallback_response
            except Exception as fallback_error:
                logger.error(f"Error with fallback model {FALLBACK_MODEL}: {str(fallback_error)}")
                
                # Record the failed call
                self.llm_calls.append(LLMCall(
                    timestamp=start_time,
                    node_id=node.id,
                    node_name=node.name,
                    node_type=str(node.type),
                    prompt=system_content,
                    user_input=user_input,
                    response=f"ERROR: Primary model ({MODEL}) error: {str(primary_error)}. Fallback model ({FALLBACK_MODEL}) error: {str(fallback_error)}",
                    tokens_used=0,
                    duration_ms=(time.time() - start_time) * 1000,
                    model=f"{MODEL} -> {FALLBACK_MODEL} (both failed)"
                ))
                
                # Default to not progressing on error
                self.context.ready_to_transition = False
                
                return f"I apologize, but I encountered an error. Please try again."
    
    def _make_api_call(self, messages, node, functions, model_name, user_input, system_content, start_time):
        """Helper method to make the actual API call with consistent parameters"""
        # Prepare API call parameters
        api_params = {
            "model": model_name,
            "messages": messages,
            "max_tokens": 600 if node.type == NodeType.ACTION else 100,
            "temperature": 0.7 if node.type == NodeType.ACTION else 0.1,
        }
        
        # Add response_format for ACTION nodes, but only if not using function calling
        if node.type == NodeType.ACTION and not functions:
            api_params["response_format"] = {"type": "json_object"}
        
        # Add functions if available - update format for Gemini compatibility
        if functions:
            api_params["tools"] = [{"type": "function", "function": func} for func in functions]
            api_params["tool_choice"] = "auto"
            # Don't use response_format with function calling in Gemini
            api_params.pop("response_format", None)
        
        # Make the API call
        completion = client.chat.completions.create(**api_params)
        
        response_message = completion.choices[0].message
        
        # Check if function was called - adjust for Gemini API compatibility
        if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
            function_call = response_message.tool_calls[0]
            
            # Check which function was called
            if function_call.function.name == "get_bike_model_info":
                # Parse function arguments
                try:
                    args = json.loads(function_call.function.arguments)
                    function_model_name = args.get("model_name")
                    comparison_models = args.get("comparison_models", [])
                    specific_feature = args.get("specific_feature")
                    
                    # Prepare function response
                    function_response = ""
                    
                    # If comparing multiple models
                    if comparison_models and len(comparison_models) > 1:
                        function_response = self._generate_comparison(comparison_models)
                    # If querying about specific feature
                    elif specific_feature and function_model_name:
                        model_specs = self.bike_data.get("BIKE_SPECS", {}).get(function_model_name, {})
                        if specific_feature in model_specs:
                            feature_name = specific_feature.replace("_", " ").title()
                            feature_value = model_specs[specific_feature]
                            function_response = f"{feature_name} for {function_model_name}: {feature_value}"
                        else:
                            function_response = f"Information about {specific_feature} for {function_model_name} is not available."
                    # If just querying about a model
                    elif function_model_name:
                        # Get model specs
                        model_specs = self.bike_data.get("BIKE_SPECS", {}).get(function_model_name, {})
                        if model_specs:
                            # Get highlights
                            highlights = self._get_spec_highlights(function_model_name)
                            function_response = f"Specifications for {function_model_name}:\n"
                            for spec, value in highlights.items():
                                spec_name = spec.replace("_", " ").title()
                                function_response += f"- {spec_name}: {value}\n"
                        else:
                            function_response = f"Information about {function_model_name} is not available."
                    
                    # Call LLM again with the function response
                    second_messages = messages.copy()
                    second_messages.append({
                        "role": "assistant",
                        "content": None,
                        "tool_calls": [
                            {
                                "id": function_call.id,
                                "type": "function",
                                "function": {
                                    "name": "get_bike_model_info",
                                    "arguments": function_call.function.arguments
                                }
                            }
                        ]
                    })
                    second_messages.append({
                        "role": "tool",
                        "tool_call_id": function_call.id,
                        "content": function_response
                    })
                    
                    # Make the second API call
                    second_api_params = {
                        "model": model_name,
                        "messages": second_messages,
                        "max_tokens": 600 if node.type == NodeType.ACTION else 100,
                        "temperature": 0.7 if node.type == NodeType.ACTION else 0.1,
                    }
                    
                    # Only add response_format for ACTION nodes when not using further tool calls
                    if node.type == NodeType.ACTION:
                        second_api_params["response_format"] = {"type": "json_object"}
                    
                    second_completion = client.chat.completions.create(**second_api_params)
                    
                    response_content = second_completion.choices[0].message.content
                except Exception as function_error:
                    logger.error(f"Error executing function: {str(function_error)}")
                    # Fall back to regular response
                    response_content = "I couldn't find information about that model. " + response_message.content
            else:
                # Unknown function
                response_content = response_message.content
        else:
            # No function called
            response_content = response_message.content
        
        # For ACTION nodes, parse the JSON response to get response text and progression decision
        if node.type == NodeType.ACTION:
            try:
                # Log the raw response for debugging
                logger.info(f"Raw response from LLM for node {node.id}: {response_content}")
                
                # First try: Extract JSON from markdown code blocks if present
                json_content = None
                
                # Check if response is wrapped in markdown code blocks
                code_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
                code_block_match = re.search(code_block_pattern, response_content)
                
                if code_block_match:
                    # Extract content from inside the code block
                    json_content = code_block_match.group(1).strip()
                    logger.info(f"Extracted content from markdown code block for node {node.id}")
                else:
                    # If no code blocks, use the raw content
                    json_content = response_content.strip()
                    logger.info(f"No code blocks found, using raw content for node {node.id}")
                
                # Ensure we have a complete JSON object
                if not (json_content.startswith('{') and json_content.endswith('}')):
                    start_idx = json_content.find('{')
                    end_idx = json_content.rfind('}')
                    if start_idx >= 0 and end_idx > start_idx:
                        json_content = json_content[start_idx:end_idx+1]
                        logger.info(f"Extracted JSON object from content for node {node.id}")
                
                # Parse the JSON - try multiple methods
                response_data = None
                parsing_methods = [
                    # Method 1: Direct JSON parsing
                    lambda content: json.loads(content),
                    
                    # Method 2: Clean and parse
                    lambda content: json.loads(clean_json_for_parsing(content)),
                    
                    # Method 3: Use ast.literal_eval
                    lambda content: ast.literal_eval(clean_json_for_parsing(content)),
                    
                    # Method 4: Brute force extraction (last resort)
                    lambda content: brute_force_json_extraction(content)
                ]
                
                # Try each parsing method until one succeeds
                for i, parse_method in enumerate(parsing_methods):
                    try:
                        response_data = parse_method(json_content)
                        logger.info(f"Successfully parsed JSON using method {i+1} for node {node.id}")
                        break
                    except Exception as e:
                        if i < len(parsing_methods) - 1:
                            logger.warning(f"Parsing method {i+1} failed: {str(e)}")
                        else:
                            logger.error(f"All JSON parsing methods failed for node {node.id}")
                            # Use brute force extraction as absolute last resort
                            response_data = brute_force_json_extraction(response_content)
                            logger.info(f"Using brute force extraction result: {response_data}")
                
                # Extract response components
                if response_data and isinstance(response_data, dict):
                    # Extract the actual response text
                    response_text = response_data.get("response", "")
                    
                    # Extract progression decision
                    should_progress = response_data.get("should_progress", "").lower() == "yes"
                    
                    # Set transition flag in context
                    self.context.ready_to_transition = should_progress
                    
                    # Process state fields if present
                    if "state_field" in response_data and isinstance(response_data["state_field"], dict):
                        for field, value in response_data["state_field"].items():
                            if field == "selected_model" and value:
                                logger.info(f"Model {value} selected from LLM response")
                                self.context.selected_model = value
                                self.context.preferences["model_selected"] = "yes"
                            elif field == "selected_showroom" and value:
                                self.context.selected_showroom = value
                                logger.info(f"Showroom {value} selected from LLM response")
                            elif field == "scheduled_time" and value:
                                self.context.scheduled_time = value
                                logger.info(f"Scheduled time {value} set from LLM response")
                            elif field == "city" and value:
                                self.context.city = value
                                # Also store in address field for more complete address information
                                self.context.address = value
                                logger.info(f"Address {value} set from LLM response")
                            elif field == "sacco_name" and value:
                                self.context.sacco_name = value
                                logger.info(f"SACCO name {value} set from LLM response")
                            elif field == "data_sharing_consent" and value is not None:
                                consent_value = str(value).lower() in ["yes", "true", "1"]
                                self.context.data_sharing_consent = consent_value
                                logger.info(f"Data sharing consent {consent_value} set from LLM response")
                    
                    # Log the transition decision for debugging
                    logger.info(f"Transition decision for node {node.name}: {should_progress}")
                    
                    # Use the extracted response text
                    response = response_text
                else:
                    # Fallback if all parsing methods failed
                    logger.warning(f"Using raw content after all parsing attempts failed for node {node.id}")
                    response = response_content
                    self.context.ready_to_transition = False
            except Exception as e:
                # Catch-all for any unhandled exceptions in the parsing process
                logger.error(f"Error processing LLM response for node {node.id}: {str(e)}", exc_info=True)
                response = response_content
                self.context.ready_to_transition = False
        else:
            # For non-ACTION nodes, use the entire response
            response = response_content
            
            # DECISION nodes always progress
            if node.type == NodeType.DECISION:
                self.context.ready_to_transition = True
            else:
                # START_END nodes never progress
                self.context.ready_to_transition = False
        
        # Calculate tokens used (estimated - Azure OpenAI doesn't always return token count)
        tokens_used = 0
        if hasattr(completion, 'usage') and completion.usage:
            if hasattr(completion.usage, 'total_tokens'):
                tokens_used = completion.usage.total_tokens
        
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000
        
        # Record this LLM call for debugging
        self.llm_calls.append(LLMCall(
            timestamp=start_time,
            node_id=node.id,
            node_name=node.name,
            node_type=str(node.type),
            prompt=system_content,
            user_input=user_input,
            response=response,
            tokens_used=tokens_used,
            duration_ms=duration_ms,
            model=model_name
        ))
        
        return response
    
    def _process_decision(self, node: PipelineNode, user_input: str) -> str:
        """Process a decision node and return the decision result"""
        # Add comprehensive debug logging for upload_national_id node
        if node.id == "upload_national_id":
            print("Uploading National ID")
            logger.info(f"==== DETAILED CONTEXT FOR upload_national_id NODE ====")
            logger.info(f"User input: '{user_input}'")
            logger.info(f"image_uploaded: {self.context.image_uploaded}")
            logger.info(f"id_upload_complete: {self.context.id_upload_complete}")
            logger.info(f"national_id: {self.context.national_id}")
            logger.info(f"first_name: {self.context.first_name}")
            logger.info(f"last_name: {self.context.last_name}")
            if hasattr(self.context, 'extracted_from_image'):
                logger.info(f"extracted_from_image: {self.context.extracted_from_image}")
            if hasattr(self.context, 'extracted_info'):
                logger.info(f"extracted_info: {self.context.extracted_info}")
            logger.info(f"current_node_id: {self.context.current_node_id}")
            logger.info(f"ready_to_transition: {self.context.ready_to_transition}")
            
            # Print full context for reference
            context_dict = self.context.dict() if hasattr(self.context, 'dict') else vars(self.context)
            print("\n" + "="*80)
            print(f"FULL CONTEXT FOR upload_national_id NODE:")
            # Print context attributes individually for better readability
            for key, value in context_dict.items():
                if key == "conversation_history":
                    print(f"  {key}: [... {len(value)} messages ...]")
                else:
                    print(f"  {key}: {value}")
            print("="*80 + "\n")
            
            # For clarity, print the last few messages from the conversation history
            if hasattr(self.context, 'conversation_history') and self.context.conversation_history:
                print("\n" + "="*80)
                print(f"LAST 3 MESSAGES FROM CONVERSATION HISTORY:")
                history = self.context.conversation_history[-3:] if len(self.context.conversation_history) >= 3 else self.context.conversation_history
                for msg in history:
                    print(f"  {msg['role']}: {msg['content'][:100]}...")
                print("="*80 + "\n")
            
            # ADDED: Explicit check for system flags to directly determine decision
            if (self.context.image_uploaded or self.context.id_upload_complete or 
                (self.context.national_id is not None and self.context.national_id != "")):
                logger.info("SYSTEM FLAGS CHECK: ID has been uploaded. Returning 'yes' decision directly.")
                return "yes"
            
            # Explicitly inform the LLM about the current state of the system flags in the prompt
            state_prefix = f"""CURRENT SYSTEM FLAGS:
image_uploaded = {self.context.image_uploaded}
id_upload_complete = {self.context.id_upload_complete}
national_id = {"Present" if self.context.national_id else "Not present"}

Based on the above system flags, determine if the user has uploaded their ID.
"""
            # Modify the node prompt temporarily to include the state information
            original_prompt = node.prompt
            node.prompt = state_prefix + node.prompt
            
            # Call LLM to make decision with the enhanced prompt
            decision_result = self._call_llm(node, user_input).strip()
            
            # Restore the original prompt
            node.prompt = original_prompt
            
        # Special handling for address_sacco_decision to check if sacco_name is already in context
        if node.id == "address_sacco_decision" and self.context.sacco_name:
            return "yes"  # If SACCO name is already in context, return yes
        
        # If this is the address_sacco_decision and we have both address/city and sacco_name in context
        if node.id == "address_sacco_decision" and (self.context.city or self.context.address) and self.context.sacco_name:
            address_value = self.context.address if self.context.address else self.context.city
            logger.info(f"Forcing 'yes' decision for address_sacco_decision - address: {address_value}, sacco: {self.context.sacco_name}")
            return "yes"
        
        # Call LLM to make decision
        decision_result = self._call_llm(node, user_input).strip()
        
        # Log the raw decision result for debugging
        logger.info(f"Raw decision result for node {node.id}: {decision_result[:100]}...")
        
        # Check if the response is wrapped in markdown code blocks
        if "```" in decision_result:
            code_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
            code_block_match = re.search(code_block_pattern, decision_result)
            if code_block_match:
                decision_json = code_block_match.group(1).strip()
                logger.info(f"Extracted decision from markdown code block for node {node.id}")
                
                # Try to parse the extracted JSON
                try:
                    cleaned_decision = clean_json_for_parsing(decision_json)
                    json_response = json.loads(cleaned_decision)
                    logger.info(f"Successfully parsed extracted decision JSON for node {node.id}")
                    
                    # Extract state fields if provided
                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                        for field, value in json_response["state_field"].items():
                            if field == "selected_model" and value:
                                logger.info(f"Model {value} selected from LLM response")
                                self.context.selected_model = value
                                self.context.preferences["model_selected"] = "yes"
                            elif field == "selected_showroom" and value:
                                self.context.selected_showroom = value
                                logger.info(f"Showroom {value} selected from LLM response")
                            elif field == "scheduled_time" and value:
                                self.context.scheduled_time = value
                                logger.info(f"Scheduled time {value} set from LLM response")
                            elif field == "city" and value:
                                self.context.city = value
                                # Also store in address field for more complete address information
                                self.context.address = value
                                logger.info(f"Address {value} set from LLM response")
                            elif field == "sacco_name" and value:
                                self.context.sacco_name = value
                                logger.info(f"SACCO name {value} set from LLM response")
                            elif field == "data_sharing_consent" and value is not None:
                                consent_value = str(value).lower() in ["yes", "true", "1"]
                                self.context.data_sharing_consent = consent_value
                                logger.info(f"Data sharing consent {consent_value} set from LLM response")
                    
                    # If this is the model_selected_decision and we have a selected model in context
                    if node.id == "model_selected_decision" and self.context.selected_model:
                        logger.info(f"Forcing 'yes' decision for model_selected_decision - model: {self.context.selected_model}")
                        return "yes"
                        
                    # Extract the decision result from JSON response
                    if "decision" in json_response:
                        decision = json_response["decision"].lower()
                        if decision in ["yes", "no"]:
                            return decision
                    if "response" in json_response:
                        return json_response["response"].strip().lower()
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse JSON from extracted decision: {str(e)}")
                    # Fall through to regular handling
        
        # Try to parse JSON response (this is the original approach)
        try:
            cleaned_decision = clean_json_for_parsing(decision_result)
            json_response = json.loads(cleaned_decision)
            
            # Extract state fields if provided
            if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                for field, value in json_response["state_field"].items():
                    if field == "selected_model" and value:
                        logger.info(f"Model {value} selected from LLM response")
                        self.context.selected_model = value
                        self.context.preferences["model_selected"] = "yes"
                    elif field == "selected_showroom" and value:
                        self.context.selected_showroom = value
                        logger.info(f"Showroom {value} selected from LLM response")
                    elif field == "scheduled_time" and value:
                        self.context.scheduled_time = value
                        logger.info(f"Scheduled time {value} set from LLM response")
                    elif field == "city" and value:
                        self.context.city = value
                        # Also store in address field for more complete address information
                        self.context.address = value
                        logger.info(f"Address {value} set from LLM response")
                    elif field == "sacco_name" and value:
                        self.context.sacco_name = value
                        logger.info(f"SACCO name {value} set from LLM response")
                    elif field == "data_sharing_consent" and value is not None:
                        consent_value = str(value).lower() in ["yes", "true", "1"]
                        self.context.data_sharing_consent = consent_value
                        logger.info(f"Data sharing consent {consent_value} set from LLM response")
                    # Add other state fields as needed
            
            # If this is the model_selected_decision and we have a selected model in context
            if node.id == "model_selected_decision" and self.context.selected_model:
                logger.info(f"Forcing 'yes' decision for model_selected_decision - model: {self.context.selected_model}")
                return "yes"
                
            # Extract the decision result from JSON response
            if "decision" in json_response:
                decision = json_response["decision"].lower()
                if decision in ["yes", "no"]:
                    return decision
            if "response" in json_response:
                decision_result = json_response["response"]
        except json.JSONDecodeError:
            logger.warning(f"Failed to parse JSON from decision response: {decision_result[:100]}...")
            # Continue with the text-based extraction if JSON parsing fails
            
            # Last resort, try brute force extraction
            brute_result = brute_force_json_extraction(decision_result)
            logger.info(f"Using brute force extraction in _process_decision: {brute_result}")
            
            # Extract decision from brute_result if available
            if brute_result.get("decision"):
                decision = brute_result["decision"].lower()
                if decision in ["yes", "no"]:
                    logger.info(f"Using brute force extracted decision: '{decision}'")
                    return decision
            if brute_result.get("response"):
                # Check if it's a yes/no decision in the extracted response
                response = brute_result["response"].strip().lower()
                if response in ["yes", "no"]:
                    logger.info(f"Using brute force extracted decision: '{response}'")
                    return response
                    
            # If we still don't have a valid decision, try basic text extraction
        
        # Try to extract JSON from the response
        json_match = re.search(r'\{.*\}', decision_result, re.DOTALL)
        if json_match:
            try:
                json_str = json_match.group(0)
                cleaned_json_str = clean_json_for_parsing(json_str)
                response_json = json.loads(cleaned_json_str)
                
                # Extract state fields
                if "state_field" in response_json and isinstance(response_json["state_field"], dict):
                    for field, value in response_json["state_field"].items():
                        if field == "selected_model" and value:
                            logger.info(f"Model {value} selected from embedded JSON")
                            self.context.selected_model = value
                            self.context.preferences["model_selected"] = "yes"
                        elif field == "selected_showroom" and value:
                            self.context.selected_showroom = value
                            logger.info(f"Showroom {value} selected from embedded JSON")
                        elif field == "scheduled_time" and value:
                            self.context.scheduled_time = value
                            logger.info(f"Scheduled time {value} set from embedded JSON")
                        elif field == "city" and value:
                            self.context.city = value
                            # Also store in address field for more complete address information
                            self.context.address = value
                            logger.info(f"Address {value} set from embedded JSON")
                        elif field == "sacco_name" and value:
                            self.context.sacco_name = value
                            logger.info(f"SACCO name {value} set from embedded JSON")
                        elif field == "data_sharing_consent" and value is not None:
                            consent_value = str(value).lower() in ["yes", "true", "1"]
                            self.context.data_sharing_consent = consent_value
                            logger.info(f"Data sharing consent {consent_value} set from embedded JSON")
                        # Add other state fields as needed
                
                # Extract decision from JSON
                if "decision" in response_json:
                    decision = response_json["decision"].lower()
                    if decision in ["yes", "no"]:
                        return decision
                elif "response" in response_json:
                    decision_result = response_json["response"]
            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"Failed to parse embedded JSON from LLM response in node {node.id}: {str(e)}")
                # Fall back to regular extraction if JSON parsing fails
        
        # If this is the model_selected_decision and we now have a selected model
        if node.id == "model_selected_decision" and self.context.selected_model:
            logger.info(f"Selected model detected during decision processing: {self.context.selected_model}")
            return "yes"
            
        # Extract the actual decision from potential longer response
        for key in node.next_nodes.keys():
            if key.lower() in decision_result.lower():
                return key
        
        # Default case if we can't clearly determine
        return list(node.next_nodes.keys())[0] if node.next_nodes else "yes"
    
    
    def _extract_model_selection(self, user_input: str) -> Optional[str]:
        """Try to extract bike model from user input"""
        for model in self.bike_models:
            if model.lower() in user_input.lower():
                return model
        
        return None
    
    def _extract_feature_inquiry(self, user_input: str) -> Optional[Dict[str, str]]:
        """Extract feature inquiries from user input"""
        feature_inquiry = {}
        
        # Check if a specific model is mentioned
        model = self._extract_model_selection(user_input)
        if model:
            feature_inquiry["model"] = model
            # Add to compared models
            self.context.add_compared_model(model)
        
        # Check for specific feature inquiries
        aliases = self.bike_data.get("SPEC_ALIASES", {})
        for alias, spec_key in aliases.items():
            if alias.lower() in user_input.lower():
                feature_inquiry["feature"] = spec_key
                break
        
        # Check for general feature categories
        for category, specs in self.feature_categories.items():
            if category.lower() in user_input.lower():
                feature_inquiry["category"] = specs
                break
        
        # Check for comparison requests
        comparison_terms = ["compare", "comparison", "difference", "vs", "versus"]
        if any(term in user_input.lower() for term in comparison_terms):
            feature_inquiry["comparison"] = True
            
            # Try to extract multiple models for comparison
            models_mentioned = []
            for model in self.bike_models:
                if model.lower() in user_input.lower():
                    models_mentioned.append(model)
                    self.context.add_compared_model(model)
                    
            if len(models_mentioned) > 1:
                feature_inquiry["models"] = models_mentioned
        
        return feature_inquiry if feature_inquiry else None
    
    def _get_feature_details(self, inquiry: Dict[str, str]) -> str:
        """Get detailed information about bike features"""
        response = ""
        bike_specs = self.bike_data.get("BIKE_SPECS", {})
        
        # Handle comparisons explicitly
        if "comparison" in inquiry and inquiry["comparison"]:
            if "models" in inquiry and len(inquiry["models"]) > 1:
                # Compare specified models
                models_to_compare = inquiry["models"]
                response = self._generate_comparison(models_to_compare)
            elif "model" in inquiry:
                # Compare this model with other recently viewed models
                model = inquiry["model"]
                compared_models = self.context.get_compared_models()
                models_to_compare = [m for m in compared_models if m != model][:2]  # Get up to 2 other models
                if models_to_compare:
                    models_to_compare.insert(0, model)  # Put the current model first
                    response = self._generate_comparison(models_to_compare)
                else:
                    # Not enough models to compare
                    response = f"I'd be happy to compare {model} with other models. Please mention which models you'd like to compare it with."
            else:
                # General comparison request without specific models
                compared_models = self.context.get_compared_models()
                if len(compared_models) >= 2:
                    # Use the models they've already viewed/compared
                    response = self._generate_comparison(compared_models[:3])  # Compare up to 3 models
                else:
                    # Not enough models viewed for comparison
                    most_popular = list(bike_specs.keys())[:3]  # Use top 3 models as a fallback
                    response = self._generate_comparison(most_popular)
            
            return response
        
        # If a specific model is mentioned
        if "model" in inquiry:
            model = inquiry["model"]
            specs = bike_specs.get(model, {})
            
            # If a specific feature is requested
            if "feature" in inquiry:
                feature = inquiry["feature"]
                if feature in specs:
                    feature_name = feature.replace("_", " ").title()
                    feature_value = specs[feature]
                    response = f"## {feature_name} for {model}\n"
                    response += f"{feature_value}\n\n"
                    
                    # Add explanations for specific features
                    if feature == "MOTOR_POWER":
                        response += "Higher kW values indicate more power, which is important for hills and heavy loads.\n"
                    elif feature == "RANGE":
                        response += "This is the maximum distance the bike can travel on a single charge under ideal conditions.\n"
                    elif feature == "PAYLOAD_CAPACITY":
                        response += "This is the maximum weight the bike can carry, including the rider.\n"
                    elif feature == "BATTERY_CAPACITY":
                        response += "Higher Ah (Ampere-hour) values indicate a larger battery capacity, which usually results in longer range.\n"
            
            # If a category of features is requested
            elif "category" in inquiry:
                category_specs = inquiry["category"]
                response = f"## {model} Specifications\n\n"
                
                for spec in category_specs:
                    if spec in specs:
                        spec_name = spec.replace("_", " ").title()
                        spec_value = specs[spec]
                        response += f"- {spec_name}: {spec_value}\n"
        
        # If only a feature is mentioned without a specific model
        elif "feature" in inquiry:
            feature = inquiry["feature"]
            response = f"## {feature.replace('_', ' ').title()} Information\n\n"
            
            # Compare this feature across models
            for model, specs in bike_specs.items():
                if feature in specs:
                    response += f"- {model}: {specs[feature]}\n"
        
        # If only a category is mentioned without a specific model
        elif "category" in inquiry:
            category_specs = inquiry["category"]
            category_name = next((cat for cat, specs in self.feature_categories.items() if specs == category_specs), "Features")
            response = f"## {category_name.title()} Information\n\n"
            
            # For each spec in the category, list values across models
            for spec in category_specs:
                spec_name = spec.replace("_", " ").title()
                response += f"### {spec_name}:\n"
                
                for model, specs in bike_specs.items():
                    if spec in specs:
                        response += f"- {model}: {specs[spec]}\n"
                
                response += "\n"
        
        return response
    
    def _generate_comparison(self, models: List[str]) -> str:
        """Generate a comparison between multiple bike models"""
        if not models or len(models) < 2:
            return "I need at least two models to compare."
        
        bike_specs = self.bike_data.get("BIKE_SPECS", {})
        
        # Determine which models actually exist in our data
        valid_models = [model for model in models if model in bike_specs]
        
        if len(valid_models) < 2:
            return "I don't have enough information about these models to compare them."
        
        # Key specifications to compare
        key_specs = [
            "RANGE", "MOTOR_POWER", "MOTOR_TYPE", "PAYLOAD_CAPACITY", 
            "SUITABLE_TERRAIN", "BATTERY_CAPACITY", "FAST_CHARGING"
        ]
        
        # Generate comparison table
        comparison = f"## Comparison of {', '.join(valid_models)}\n\n"
        
        # For each key specification, compare across models
        for spec in key_specs:
            spec_name = spec.replace("_", " ").title()
            comparison += f"### {spec_name}:\n"
            
            for model in valid_models:
                model_specs = bike_specs.get(model, {})
                spec_value = model_specs.get(spec, "N/A")
                if isinstance(spec_value, list) and spec_value:
                    spec_value = spec_value[0]  # Take first value for lists
                
                comparison += f"- {model}: {spec_value}\n"
            
            comparison += "\n"
        
        # Add a brief summary highlighting key differences
        comparison += "## Key Differences\n"
        
        # Check for terrain differences
        terrain_values = {}
        for model in valid_models:
            terrain = bike_specs.get(model, {}).get("SUITABLE_TERRAIN", "")
            if terrain:
                if terrain not in terrain_values:
                    terrain_values[terrain] = []
                terrain_values[terrain].append(model)
        
        if len(terrain_values) > 1:
            comparison += "### Terrain Suitability:\n"
            for terrain, models_list in terrain_values.items():
                comparison += f"- {terrain}: {', '.join(models_list)}\n"
            comparison += "\n"
        
        # Check for range differences
        range_values = {}
        for model in valid_models:
            range_val = bike_specs.get(model, {}).get("RANGE", "")
            if range_val:
                if range_val not in range_values:
                    range_values[range_val] = []
                range_values[range_val].append(model)
        
        if len(range_values) > 1:
            comparison += "### Range:\n"
            for range_val, models_list in range_values.items():
                comparison += f"- {range_val}: {', '.join(models_list)}\n"
            comparison += "\n"
        
        # Add price comparison if we can calculate prices
        comparison += "### Price Comparison:\n"
        for model in valid_models:
            price_details = self._calculate_price(model)
            comparison += f"- {model}: ₹{price_details['final_price']:,.2f}\n"
        
        return comparison
    
    def _find_matching_bikes(self, requirements: dict) -> List[str]:
        """Find bikes that match the given requirements"""
        matches = []
        bike_specs = self.bike_data.get("BIKE_SPECS", {})
        aliases = self.bike_data.get("SPEC_ALIASES", {})
        
        # Convert requirement keys to actual spec keys using aliases
        normalized_requirements = {}
        for req_key, req_value in requirements.items():
            req_key_lower = req_key.lower()
            if req_key_lower in aliases:
                actual_key = aliases[req_key_lower]
                normalized_requirements[actual_key] = req_value
            else:
                # Try to find a close match in the aliases
                for alias_key, spec_key in aliases.items():
                    if alias_key in req_key_lower or req_key_lower in alias_key:
                        normalized_requirements[spec_key] = req_value
                        break
        
        # Filter bikes based on requirements
        for model, specs in bike_specs.items():
            matches_all = True
            
            for spec_key, req_value in normalized_requirements.items():
                if spec_key not in specs:
                    continue
                
                spec_value = specs[spec_key]
                
                # Handle range comparisons (e.g., "<95 KM")
                if isinstance(spec_value, str) and spec_value.startswith("<"):
                    # For range, higher is better
                    if "range" in spec_key.lower():
                        if "high" in req_value.lower() or "long" in req_value.lower():
                            matches_all = False
                            break
                
                # Handle payload capacity
                if spec_key == "PAYLOAD_CAPACITY" and isinstance(spec_value, str):
                    capacity = int(spec_value.replace("<", "").replace(" KG", ""))
                    if "heavy" in req_value.lower() and capacity < 240:
                        matches_all = False
                        break
                
                # Handle terrain suitability
                if spec_key == "SUITABLE_TERRAIN":
                    if "hill" in req_value.lower() and "hill" not in spec_value.lower():
                        matches_all = False
                        break
                
                # Handle motor power for performance needs
                if spec_key == "MOTOR_POWER" and isinstance(spec_value, str):
                    if "powerful" in req_value.lower() or "fast" in req_value.lower():
                        if not any(p in spec_value for p in ["7.5", "9.4", "9"]):
                            matches_all = False
                            break
            
            if matches_all:
                matches.append(model)
        
        # If no matches, return top 3 models as fallback
        if not matches and bike_specs:
            return list(bike_specs.keys())[:3]
        
        return matches[:3]  # Return top 3 matches
    
    def _get_spec_highlights(self, model: str) -> Dict[str, str]:
        """Get key specifications to highlight for a model"""
        specs = self.bike_data.get("BIKE_SPECS", {}).get(model, {})
        highlights = {}
        
        key_specs = ["RANGE", "MOTOR_POWER", "MOTOR_TYPE", "PAYLOAD_CAPACITY", "SUITABLE_TERRAIN", 
                    "BATTERY_CAPACITY", "FAST_CHARGING"]
        
        for spec in key_specs:
            if spec in specs:
                highlights[spec] = specs[spec]
        
        return highlights
    
    def _extract_requirements(self, user_input: str) -> Dict[str, str]:
        """Extract requirements from user input"""
        requirements = {}
        
        # Check for terrain preferences
        if any(word in user_input.lower() for word in ["hill", "mountain", "steep"]):
            requirements["SUITABLE_TERRAIN"] = "hills"
        elif any(word in user_input.lower() for word in ["flat", "plain", "city"]):
            requirements["SUITABLE_TERRAIN"] = "plains"
        
        # Check for range needs
        if any(word in user_input.lower() for word in ["long", "far", "distance"]):
            requirements["RANGE"] = "long range"
        
        # Check for load capacity needs
        if any(word in user_input.lower() for word in ["heavy", "cargo", "load", "passenger"]):
            requirements["PAYLOAD_CAPACITY"] = "heavy load"
        
        # Check for performance needs
        if any(word in user_input.lower() for word in ["fast", "powerful", "quick", "speed"]):
            requirements["MOTOR_POWER"] = "powerful"
        
        return requirements
    
    def get_debug_info(self) -> List[Dict[str, Any]]:
        """Get detailed information about all LLM calls for debugging"""
        debug_info = []
        for call in self.llm_calls:
            # Convert to dict and format timestamps for readability
            call_dict = call.dict()
            
            # Format timestamp as readable time
            timestamp = time.strftime('%H:%M:%S', time.localtime(call_dict['timestamp']))
            call_dict['timestamp'] = timestamp
            
            # Round duration to 2 decimal places
            call_dict['duration_ms'] = round(call_dict['duration_ms'], 2)
            
            debug_info.append(call_dict)
            
        return debug_info
    
    def process_step(self, user_input: str = "") -> Dict[str, Any]:
        """Process the current node in the pipeline and determine the next node"""
        current_node = self.nodes.get(self.context.current_node_id)
        if not current_node:
            return {"error": f"Invalid node ID: {self.context.current_node_id}"}
        
        # Add user input to context if provided - ONLY if not already added in continue_conversation
        # Check if the last message in history is from the user with the same content
        should_add_user_message = user_input and (
            not self.context.conversation_history or 
            self.context.conversation_history[-1].get('role') != 'user' or 
            self.context.conversation_history[-1].get('content') != user_input
        )
        
        if should_add_user_message:
            self.context.add_message("user", user_input)
            
        # Increment interaction count for this node if there's user input
        if user_input:
            self.context.increment_node_interaction(self.context.current_node_id)
            
            # Try to extract model selection from user input using regex
            if not self.context.selected_model:
                selected_model = self._extract_model_selection(user_input)
                if selected_model:
                    self.context.selected_model = selected_model
                    self.context.preferences["model_selected"] = "yes"
                    logger.info(f"Model {selected_model} selected via regex extraction")
                    
                    # When a model is selected directly from display_models, immediately transition to price_calculation
                    if current_node.id == "display_models":
                        # Force immediate transition through model_selected_decision to price_calculation
                        logger.info(f"Model {selected_model} selected from display_models, forcing transition to pricing")
                        self.context.ready_to_transition = True
                        
                        # First transition to model_selected_decision
                        next_node_id = current_node.get_next_node("default")  # This should be model_selected_decision
                        self.context.current_node_id = next_node_id
                        
                        # Then get the next node from model_selected_decision with "yes" result
                        decision_node = self.nodes.get(next_node_id)
                        if decision_node:
                            price_node_id = decision_node.get_next_node("yes")
                            if price_node_id:
                                # Transition directly to price_calculation
                                self.context.current_node_id = price_node_id
                                price_node = self.nodes.get(price_node_id)
                                
                                # Call LLM to get pricing information
                                response_message = self._call_llm(price_node, "")
                                
                                # Try to parse JSON response
                                try:
                                    json_response = json.loads(response_message)
                                    response_message = json_response.get("response", response_message)
                                    
                                    # Update state fields if provided
                                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                                        for field, value in json_response["state_field"].items():
                                            if field == "selected_model" and value:
                                                self.context.selected_model = value
                                            elif field == "selected_showroom" and value:
                                                self.context.selected_showroom = value
                                                logger.info(f"Showroom {value} selected from LLM response")
                                            elif field == "scheduled_time" and value:
                                                self.context.scheduled_time = value
                                                logger.info(f"Scheduled time {value} set from LLM response")
                                            # Add other state fields as needed
                                except json.JSONDecodeError:
                                    logger.warning(f"Failed to parse JSON from LLM response: {response_message[:100]}...")
                                
                                self.context.add_message("assistant", response_message)
                                
                                # Return with pricing information and normal text display
                                return {
                                    "success": True,
                                    "message": response_message,
                                    "message_type": "text",  # Switch back to text mode for pricing
                                    "flow_type": None,       # No special flow needed
                                    "current_node": price_node.name,
                                    "next_node": price_node.get_next_node("default"),
                                    "llm_call_count": self.llm_call_count,
                                    "debug_info": self.get_debug_info(),
                                    "show_upload": True,
                                    "missing_fields": [],
                                    "image_required": False
                                }
                    
                    # Also handle direct model selection in model_selected_decision node
                    elif current_node.id == "model_selected_decision":
                        price_node_id = current_node.get_next_node("yes")
                        if price_node_id:
                            # Transition directly to price_calculation
                            self.context.current_node_id = price_node_id
                            price_node = self.nodes.get(price_node_id)
                            
                            # Call LLM to get pricing information
                            response_message = self._call_llm(price_node, "")
                            
                            # Try to parse JSON response
                            try:
                                json_response = json.loads(response_message)
                                response_message = json_response.get("response", response_message)
                                
                                # Update state fields if provided
                                if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                                    for field, value in json_response["state_field"].items():
                                        if field == "selected_model" and value:
                                            self.context.selected_model = value
                                        elif field == "selected_showroom" and value:
                                            self.context.selected_showroom = value
                                            logger.info(f"Showroom {value} selected from LLM response")
                                        elif field == "scheduled_time" and value:
                                            self.context.scheduled_time = value
                                            logger.info(f"Scheduled time {value} set from LLM response")
                                        # Add other state fields as needed
                            except json.JSONDecodeError:
                                logger.warning(f"Failed to parse JSON from LLM response: {response_message[:100]}...")
                            
                            self.context.add_message("assistant", response_message)
                            
                            # Return with pricing information
                            return {
                                "success": True,
                                "message": response_message,
                                "message_type": "text",
                                "flow_type": None,
                                "current_node": price_node.name,
                                "next_node": price_node.get_next_node("default"),
                                "llm_call_count": self.llm_call_count,
                                "debug_info": self.get_debug_info(),
                                "show_upload": True,
                                "missing_fields": [],
                                "image_required": False
                            }
            
            # Store special decision results
            if current_node.id == "interested_decision":
                self.context.is_interested = "yes" in user_input.lower()
            elif current_node.id == "id_documentation_check":
                self.context.has_documents = "yes" in user_input.lower()
            elif current_node.id == "emi_decision":
                self.context.wants_emi = "yes" in user_input.lower()
            elif current_node.id == "scheduling":
                # For scheduling node, we'll extract from JSON response later
                pass
            
            # After address_sacco nodes, ensure sacco_name has at least a default value
            if "address_sacco" in current_node.id and not self.context.sacco_name:
                # Set a default SACCO name if none provided
                self.context.sacco_name = "Unknown SACCO"
            
            # Try to extract Sacco name
            sacco_match = re.search(r"(?:my sacco|sacco name|sacco)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
            if sacco_match:
                self.context.sacco_name = sacco_match.group(1).strip()
                logger.info(f"Extracted SACCO name '{self.context.sacco_name}' from regex")
            
            # Try to extract city
            city_match = re.search(r"(?:my city|city|town|location)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
            if city_match:
                self.context.city = city_match.group(1).strip()
                logger.info(f"Extracted city '{self.context.city}' from regex")
            
            # Try to extract missing fields from text
            missing_fields = self.context.get_missing_fields()
            
            # Process name
            if "name" in missing_fields:
                name_match = re.search(r"(?:my name|name)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if name_match:
                    full_name = name_match.group(1).strip()
                    if " " in full_name:
                        parts = full_name.split(" ", 1)
                        self.context.first_name = parts[0]
                        self.context.last_name = parts[1]
                    else:
                        self.context.first_name = full_name
            
            # Process national ID
            if "id_number" in missing_fields:
                id_match = re.search(r"(?:my id|national id|id number)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if id_match:
                    self.context.national_id = id_match.group(1).strip()
            
            # Process country
            if "country" in missing_fields:
                country_match = re.search(r"(?:country|nation)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if country_match:
                    self.context.country = country_match.group(1).strip()
            
            # Process state
            if "state" in missing_fields:
                state_match = re.search(r"(?:state|province)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if state_match:
                    self.context.state = state_match.group(1).strip()
            
            # Process city
            if "city" in missing_fields:
                city_match = re.search(r"(?:city|town)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if city_match:
                    self.context.city = city_match.group(1).strip()
            
            # Process zip code
            if "zip_code" in missing_fields:
                zip_match = re.search(r"(?:zip|postal|code)(?:\s*is|:)\s*([^\.]+)", user_input, re.IGNORECASE)
                if zip_match:
                    self.context.zip_code = zip_match.group(1).strip()
        
        # Special handling for model_selected_decision node
        # If we're at the model selection decision node and a model has been selected, 
        # force a "yes" decision to proceed to pricing
        if current_node.id == "model_selected_decision" and self.context.selected_model:
            result = "yes"
            next_node_id = current_node.get_next_node(result)
            self.context.preferences["model_selected"] = result
            self.context.ready_to_transition = True
            
            if next_node_id:
                self.context.current_node_id = next_node_id
                next_node = self.nodes.get(next_node_id)
                response_message = self._call_llm(next_node, "")
                
                # Try to parse JSON response
                try:
                    json_response = json.loads(response_message)
                    response_message = json_response.get("response", response_message)
                    
                    # Update state fields if provided
                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                        for field, value in json_response["state_field"].items():
                            if field == "selected_model" and value:
                                self.context.selected_model = value
                            elif field == "selected_showroom" and value:
                                self.context.selected_showroom = value
                                logger.info(f"Showroom {value} selected from LLM response")
                            elif field == "scheduled_time" and value:
                                self.context.scheduled_time = value
                                logger.info(f"Scheduled time {value} set from LLM response")
                            # Add other state fields as needed
                    self.context.add_message("assistant", response_message)
                    
                    # Set up the response with the new node
                    return {
                        "success": True,
                        "message": response_message,
                        "message_type": "text",
                        "flow_type": None,
                        "current_node": next_node.name,
                        "next_node": next_node.get_next_node("default"),
                        "llm_call_count": self.llm_call_count,
                        "debug_info": self.get_debug_info(),
                        "show_upload": True,
                        "missing_fields": [],
                        "image_required": False
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from LLM response: {response_message[:100]}...")
                
                self.context.add_message("assistant", response_message)
                
                # Set up the response with the new node
                return {
                    "success": True,
                    "message": response_message,
                    "message_type": "text",
                    "flow_type": None,
                    "current_node": next_node.name,
                    "next_node": next_node.get_next_node("default"),
                    "llm_call_count": self.llm_call_count,
                    "debug_info": self.get_debug_info(),
                    "show_upload": True,
                    "missing_fields": [],
                    "image_required": False
                }
        
        # Special handling for address_sacco_decision node
        # If we're at the address/sacco decision node and both city and sacco_name have been provided, 
        # force a "yes" decision to proceed to the next step
        if current_node.id == "address_sacco_decision" and self.context.city and self.context.sacco_name:
            result = "yes"
            next_node_id = current_node.get_next_node(result)
            self.context.preferences["address_sacco"] = result
            self.context.ready_to_transition = True
            address_value = self.context.address if self.context.address else self.context.city
            logger.info(f"Forcing 'yes' decision for address_sacco_decision - address: {address_value}, sacco: {self.context.sacco_name}")
            
            if next_node_id:
                self.context.current_node_id = next_node_id
                next_node = self.nodes.get(next_node_id)
                response_message = self._call_llm(next_node, "")
                
                # Try to parse JSON response
                try:
                    cleaned_json = clean_json_for_parsing(response_message)
                    json_response = json.loads(cleaned_json)
                    response_message = json_response.get("response", response_message)
                    
                    # Update state fields if provided
                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                        for field, value in json_response["state_field"].items():
                            if field == "city" and value:
                                self.context.city = value
                                logger.info(f"City {value} selected from LLM response")
                            elif field == "sacco_name" and value:
                                self.context.sacco_name = value
                                logger.info(f"SACCO name {value} set from LLM response")
                                logger.info(f"SACCO name {value} set from LLM response")
                            elif field == "data_sharing_consent" and value is not None:
                                consent_value = str(value).lower() in ["yes", "true", "1"]
                                self.context.data_sharing_consent = consent_value
                                logger.info(f"Data sharing consent {consent_value} set from LLM response")
                            # Add other state fields as needed
                    self.context.add_message("assistant", response_message)
                    
                    # Set up the response with the new node
                    return {
                        "success": True,
                        "message": response_message,
                        "message_type": "text",
                        "flow_type": None,
                        "current_node": next_node.name,
                        "next_node": next_node.get_next_node("default"),
                        "llm_call_count": self.llm_call_count,
                        "debug_info": self.get_debug_info(),
                        "show_upload": True,
                        "missing_fields": [],
                        "image_required": False
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from LLM response: {response_message[:100]}...")
                
                self.context.add_message("assistant", response_message)
                
                # Set up the response with the new node
                return {
                    "success": True,
                    "message": response_message,
                    "message_type": "text",
                    "flow_type": None,
                    "current_node": next_node.name,
                    "next_node": next_node.get_next_node("default"),
                    "llm_call_count": self.llm_call_count,
                    "debug_info": self.get_debug_info(),
                    "show_upload": True,
                    "missing_fields": [],
                    "image_required": False
                }
                
        # Special handling for model_selected_decision node
        if current_node.id == "model_selected_decision" and self.context.selected_model:
            result = "yes"
            next_node_id = current_node.get_next_node(result)
            self.context.preferences["model_selected"] = result
            self.context.ready_to_transition = True
            
            if next_node_id:
                self.context.current_node_id = next_node_id
                next_node = self.nodes.get(next_node_id)
                response_message = self._call_llm(next_node, "")
                
                # Try to parse JSON response
                try:
                    json_response = json.loads(response_message)
                    response_message = json_response.get("response", response_message)
                    
                    # Update state fields if provided
                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                        for field, value in json_response["state_field"].items():
                            if field == "selected_model" and value:
                                self.context.selected_model = value
                            elif field == "selected_showroom" and value:
                                self.context.selected_showroom = value
                                logger.info(f"Showroom {value} selected from LLM response")
                            elif field == "scheduled_time" and value:
                                self.context.scheduled_time = value
                                logger.info(f"Scheduled time {value} set from LLM response")
                            # Add other state fields as needed
                    self.context.add_message("assistant", response_message)
                    
                    # Set up the response with the new node
                    return {
                        "success": True,
                        "message": response_message,
                        "message_type": "text",
                        "flow_type": None,
                        "current_node": next_node.name,
                        "next_node": next_node.get_next_node("default"),
                        "llm_call_count": self.llm_call_count,
                        "debug_info": self.get_debug_info(),
                        "show_upload": True,
                        "missing_fields": [],
                        "image_required": False
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON from LLM response: {response_message[:100]}...")
                
                self.context.add_message("assistant", response_message)
                
                # Set up the response with the new node
                return {
                    "success": True,
                    "message": response_message,
                    "message_type": "text",
                    "flow_type": None,
                    "current_node": next_node.name,
                    "next_node": next_node.get_next_node("default"),
                    "llm_call_count": self.llm_call_count,
                    "debug_info": self.get_debug_info(),
                    "show_upload": True,
                    "missing_fields": [],
                    "image_required": False
                }
        
        # Process based on node type
        response_message = ""
        processed_node = None  # Track the node we actually processed
        
        if current_node.type == NodeType.DECISION:
            # For decision nodes, we process the decision but don't generate a user-visible response
            result = self._process_decision(current_node, user_input)
            next_node_id = current_node.get_next_node(result)
            
            # Store decision in user context
            key = current_node.id.replace("_decision", "")
            self.context.preferences[key] = result
            
            # Force transition for decision nodes
            self.context.ready_to_transition = True
            
            # CHANGE: Always process connected action node immediately after decision
            if next_node_id:
                # Update current node to the next node
                self.context.current_node_id = next_node_id
                next_node = self.nodes.get(next_node_id)
                
                # Process the next node immediately to get a response
                if next_node and next_node.type != NodeType.DECISION:
                    response_message = self._call_llm(next_node, "")
                    self.context.add_message("assistant", response_message)
                    
                    # Update next node ID to the following node after the action
                    next_node_id = next_node.get_next_node("default")
                    
                    # Track what node we processed for message_type determination
                    processed_node = next_node
                else:
                    # If next node is another decision, also process it immediately
                    if next_node and next_node.type == NodeType.DECISION:
                        # Recursive call to process the next decision node
                        decision_result = self.process_step("")
                        response_message = decision_result.get("message", "")
                        next_node_id = None  # Already processed in recursive call
                        
                        # Use the same message_type and flow_type from the recursive call
                        message_type = decision_result.get("message_type", "text")
                        flow_type = decision_result.get("flow_type", None)
                        processed_node = None  # Already handled in recursive call
                        
        else:  # ACTION or START_END node
            # Generate response from LLM
            response_message = self._call_llm(current_node, user_input)
            self.context.add_message("assistant", response_message)
            
            # Get next node
            next_node_id = current_node.get_next_node("default")
            processed_node = current_node  # Track the node we processed
        
        # Update current node for subsequent calls only if ready to transition
        # Decision nodes always transition (they are immediate)
        should_transition = self.context.ready_to_transition
        
        # Additional check for process_national_id node - never transition without image upload
        if current_node.id == "process_national_id" and not self.context.image_uploaded:
            # We're removing this blocker as requested
            # should_transition = False
            # Instead, always allow transition if we're at this node
            should_transition = True
        
        # Special handling for share_showroom to ensure transition to ask_feedback
        # Commented out because we've updated the flow to go from showroom → scheduling → feedback
        # if current_node.id == "share_showroom" and self.context.get_node_interactions(current_node.id) >= 1:
        #     should_transition = True
        #     logger.info("Forcing transition from share_showroom to ask_feedback")
        
        if next_node_id and should_transition:
            self.context.current_node_id = next_node_id
            self.context.ready_to_transition = False  # Reset transition flag
            next_node = self.nodes.get(next_node_id)
        else:
            next_node = current_node  # Stay on the same node
        
        # Set default message_type and flow_type
        message_type = "text"
        flow_type = None
        
        # Special handling for display_models node
        # Only use the flow view if we don't have a selected model yet
        if processed_node and processed_node.id == "display_models" and not self.context.selected_model:
            message_type = "flow"
            flow_type = "EV_MODELS"
        
        # If a model has been selected and we're in display_models, switch to text
        if self.context.selected_model and current_node.id == "display_models":
            message_type = "text"
            flow_type = None
        
        # Also ensure model_selected_decision shows text when a model is selected
        if current_node.id == "model_selected_decision" and self.context.selected_model:
            message_type = "text"  # Change back to text for pricing info
            flow_type = None  # No special flow for pricing
        
        # Special handling for display_models node to force transition if we've viewed multiple models
        if current_node.id == "display_models" and self.context.ready_to_transition:
            logger.info("Forcing transition from display_models due to ready_to_transition flag")
            self.context.ready_to_transition = False
            # Create a default response instead of returning undefined result
            default_result = {
                "success": True,
                "message": response_message,
                "message_type": message_type,
                "flow_type": flow_type,
                "current_node": current_node.name,
                "next_node": next_node.name if next_node else "None",
                "llm_call_count": self.llm_call_count,
                "debug_info": self.get_debug_info(),
                "show_upload": True,
                "missing_fields": [],
                "image_required": False
            }
            return default_result
                
        # Special handling for share_showroom to ensure transition to scheduling
        if current_node.id == "share_showroom" and self.context.selected_showroom:
            logger.info(f"Selected showroom detected: {self.context.selected_showroom}, transitioning to scheduling")
            # Create a default response instead of returning undefined result
            default_result = {
                "success": True,
                "message": response_message,
                "message_type": message_type,
                "flow_type": flow_type,
                "current_node": current_node.name,
                "next_node": next_node.name if next_node else "None",
                "llm_call_count": self.llm_call_count,
                "debug_info": self.get_debug_info(),
                "show_upload": True,
                "missing_fields": [],
                "image_required": False
            }
            return default_result
                
        # Special handling for scheduling to ensure transition to ask_feedback when time is provided
        if current_node.id == "scheduling" and self.context.scheduled_time:
            logger.info(f"Scheduled time set: {self.context.scheduled_time}, transitioning to ask_feedback")
            
            # Create a response message including reference_id if available
            custom_response = response_message
            if self.context.reference_id and self.context.reference_id not in response_message:
                # Add reference_id to the response if not already mentioned
                custom_response = f"{response_message}\n\nYour booking reference number is: {self.context.reference_id}"
                # Update the stored response message
                response_message = custom_response
                
            # Create a default response instead of returning undefined result
            default_result = {
                "success": True,
                "message": response_message,
                "message_type": message_type,
                "flow_type": flow_type,
                "current_node": current_node.name,
                "next_node": next_node.name if next_node else "None",
                "llm_call_count": self.llm_call_count,
                "debug_info": self.get_debug_info(),
                "show_upload": True,
                "missing_fields": [],
                "image_required": False
            }
            return default_result
        
        # Return the result with debug info and show_upload flag
        return {
            "success": True,
            "message": response_message,
            "message_type": message_type,
            "flow_type": flow_type,
            "current_node": current_node.name,
            "next_node": next_node.name if next_node else "None",
            "llm_call_count": self.llm_call_count,
            "debug_info": self.get_debug_info(),
            "show_upload": True,  # Always show upload button
            "missing_fields": [],  # Always return empty list for missing fields
            "image_required": False  # Image is not required but always available
        }
    
    def process_image_upload(self, image_data: str) -> Dict[str, Any]:
        """Handle an image upload from the user"""
        # Immediately set the image upload flags to True
        self.context.image_uploaded = True
        self.context.id_upload_complete = True
        
        # Log the state change for debugging
        logger.info("==== IMAGE UPLOAD FLAGS SET ====")
        logger.info(f"image_uploaded flag set to: {self.context.image_uploaded}")
        logger.info(f"id_upload_complete flag set to: {self.context.id_upload_complete}")
        
        # Call the image upload handler
        result = handle_image_upload(image_data, self)
        
        # Get the current node
        current_node = self.nodes.get(self.context.current_node_id)
        
        # For national ID upload, handle the special flow
        if current_node and current_node.id in ["process_national_id", "upload_national_id"]:
            # These flags are already set at the top of the method, but we'll set them again for clarity
            self.context.image_uploaded = True
            self.context.id_upload_complete = True
            
            # Add a system message to the conversation history about the image upload
            self.context.add_message("system", "User has uploaded a National ID document image. The image upload was successful.")
            
            # Prepare context information to show in the response
            context_info = {}
            if self.context.first_name or self.context.last_name:
                name_parts = []
                if self.context.first_name:
                    name_parts.append(self.context.first_name)
                if self.context.last_name:
                    name_parts.append(self.context.last_name)
                context_info["name"] = " ".join(name_parts)
            
            if self.context.national_id:
                context_info["id_number"] = self.context.national_id
            
            if self.context.city:
                context_info["city"] = self.context.city
                
            if self.context.sacco_name:
                context_info["sacco_name"] = self.context.sacco_name
            
            # Check what information was successfully extracted
            extracted_fields = []
            if self.context.first_name or self.context.last_name:
                extracted_fields.append("name")
            if self.context.national_id:
                extracted_fields.append("ID number")
            if self.context.city:
                extracted_fields.append("city")
            
            # Set up the response to confirm image upload and explain next steps
            if extracted_fields:
                response_message = f"""Great! I've received and processed your ID document successfully. I've extracted your {', '.join(extracted_fields)}.

Thank you for uploading this - it helps us move your application forward more quickly. Let's continue with the next steps."""
            else:
                response_message = """Thanks for uploading your ID document! I've received it.

Could you please provide:
- Your full name
- Your ID number
- Your city
- Your SACCO name (if applicable)

This will help us complete your application."""
            
            # Add to conversation history
            self.context.add_message("assistant", response_message)
            
            # Get the next node in the flow (stay on current node if we're in process_national_id)
            next_node_id = current_node.id
            next_node = current_node
            
            # Set up the response with next steps
            return {
                "success": True,
                "message": response_message,
                "message_type": "text",
                "flow_type": None,
                "current_node": current_node.name,
                "next_node": next_node.name,
                "llm_call_count": self.llm_call_count,
                "debug_info": self.get_debug_info(),
                "show_upload": False,
                "extracted_info": context_info,
                "missing_fields": self.context.get_missing_fields(),
                "image_required": False,
                "image_uploaded": True,
                "id_upload_complete": True
            }
        else:
            # Default handling for other image uploads
            if result.get("success", False):
                response_message = "Thank you for uploading the image. I've processed it successfully."
            else:
                response_message = "I couldn't process your image properly. Please try again or let me help you manually."
            
            # Add to conversation history
            self.context.add_message("assistant", response_message)
            
            # Return a generic response
            return {
                "success": result.get("success", False),
                "message": response_message,
                "message_type": "text",
                "flow_type": None,
                "current_node": current_node.name if current_node else "Unknown",
                "next_node": "None",
                "llm_call_count": self.llm_call_count,
                "debug_info": self.get_debug_info(),
                "show_upload": False,
                "missing_fields": self.context.get_missing_fields() if hasattr(self.context, "get_missing_fields") else [],
                "image_required": False,
                "image_uploaded": self.context.image_uploaded,
                "id_upload_complete": self.context.id_upload_complete if hasattr(self.context, "id_upload_complete") else False
            }

    def start_conversation(self) -> Dict[str, Any]:
        """Start the conversation with the initial node"""
        # Preserve conversation history if it exists
        existing_history = []
        if hasattr(self.context, 'conversation_history'):
            existing_history = self.context.conversation_history
        
        # Reset context attributes but keep history
        self.context = UserContext()
        self.context.conversation_history = existing_history
        self.context.node_interactions = {}
        self.context.ready_to_transition = False
        self.context.current_node_id = "start"
        
        # Reset LLM tracking
        self.llm_call_count = 0
        self.llm_calls = []
        
        # Ensure nodes are initialized
        if not self.nodes:
            self._initialize_pipeline()
        
        # Get the initial node
        initial_node = self.nodes.get("start")
        if not initial_node:
            return {
                "success": False,
                "error": "Initial node not found",
                "message": "Sorry, I couldn't start the conversation."
            }
        
        # Generate greeting message from LLM
        greeting_message = self._call_llm(initial_node, "")
        
        # Add greeting to conversation history
        self.context.add_message("assistant", greeting_message)
        
        # Get next node
        next_node_id = initial_node.get_next_node("default")
        if next_node_id:
            self.context.current_node_id = next_node_id
            next_node = self.nodes.get(next_node_id)
        else:
            next_node = None
        
        # Return the result with debug info
        return {
            "success": True,
            "message": greeting_message,
            "message_type": "text",  # Default message type is text
            "flow_type": None,  # Default flow type is None
            "current_node": initial_node.name,
            "next_node": next_node.name if next_node else "None",
            "llm_call_count": self.llm_call_count,
            "debug_info": self.get_debug_info(),
            "show_upload": True,  # Always show upload button
            "image_required": False  # Image is not required but always available
        }

    def continue_conversation(self, user_input: str) -> Dict[str, Any]:
        """Continue a conversation with new user input"""
        # Ensure nodes are initialized
        if not self.nodes:
            self._initialize_pipeline()
            
        # Add message to conversation history
        self.context.add_message("user", user_input)
        
        # Try to extract model selection from user input
        if not self.context.selected_model:
            selected_model = self._extract_model_selection(user_input)
            if selected_model:
                logger.info(f"Model {selected_model} selected during continue_conversation")
                self.context.selected_model = selected_model
                self.context.preferences["model_selected"] = "yes"
                self.context.ready_to_transition = True
        
        # Process the next step first to get the response message
        result = self.process_step(user_input)
        
        # Then try to parse the LLM response in case it contains structured state information
        try:
            if isinstance(result.get("message"), str):
                cleaned_json = clean_json_for_parsing(result["message"])
                json_response = json.loads(cleaned_json)
                if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                    for field, value in json_response["state_field"].items():
                        if field == "selected_model" and value:
                            logger.info(f"Model {value} selected from LLM response in continue_conversation")
                            self.context.selected_model = value
                            self.context.preferences["model_selected"] = "yes"
                        elif field == "selected_showroom" and value:
                            logger.info(f"Showroom {value} selected from LLM response in continue_conversation")
                            self.context.selected_showroom = value
                        elif field == "scheduled_time" and value:
                            logger.info(f"Scheduled time {value} set from LLM response in continue_conversation")
                            self.context.scheduled_time = value
                        elif field == "city" and value:
                            logger.info(f"City {value} set from LLM response in continue_conversation")
                            self.context.city = value
                        elif field == "sacco_name" and value:
                            logger.info(f"SACCO name {value} set from LLM response in continue_conversation")
                            self.context.sacco_name = value
                        elif field == "data_sharing_consent" and value is not None:
                            logger.info(f"Data sharing consent {value} set from LLM response in continue_conversation")
                            self.context.data_sharing_consent = value == "yes" or value == True
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON from LLM response in continue_conversation: {result.get('message', '')[:100]}... Error: {str(e)}")
        
        # Check if response contains JSON and extract relevant data
        if isinstance(result.get("message"), str):
            try:
                # Check if the response contains JSON
                json_match = re.search(r'\{.*\}', result["message"], re.DOTALL)
                if json_match:
                    json_str = json_match.group(0)
                    json_response = json.loads(json_str)
                    
                    # If it's a full JSON response, extract the human-readable message
                    if "response" in json_response:
                        result["message"] = json_response["response"]
                    
                    # Also check for state fields and update accordingly
                    if "state_field" in json_response and isinstance(json_response["state_field"], dict):
                        for field, value in json_response["state_field"].items():
                            if field == "selected_model" and value:
                                logger.info(f"Model {value} selected from JSON response in continue_conversation")
                                self.context.selected_model = value
                                self.context.preferences["model_selected"] = "yes"
                            elif field == "selected_showroom" and value:
                                logger.info(f"Showroom {value} selected from JSON response in continue_conversation")
                                self.context.selected_showroom = value
                            elif field == "scheduled_time" and value:
                                logger.info(f"Scheduled time {value} set from JSON response in continue_conversation")
                                self.context.scheduled_time = value
                            elif field == "city" and value:
                                logger.info(f"City {value} set from JSON response in continue_conversation")
                                self.context.city = value
                            elif field == "sacco_name" and value:
                                logger.info(f"SACCO name {value} set from JSON response in continue_conversation")
                                self.context.sacco_name = value
                            elif field == "data_sharing_consent" and value is not None:
                                logger.info(f"Data sharing consent {value} set from JSON response in continue_conversation")
                                self.context.data_sharing_consent = value == "yes" or value == True
            except (json.JSONDecodeError, AttributeError) as e:
                logger.warning(f"Error parsing JSON in continue_conversation: {str(e)}")
        
        return result

    def _prepare_context_information(self) -> str:
        """Prepare context information about the user for the prompt"""
        context = "## USER STATE INFORMATION:\n"
        
        # Personal information
        context += "### Personal Information:\n"
        if self.context.first_name or self.context.last_name:
            full_name = f"{self.context.first_name or ''} {self.context.last_name or ''}".strip()
            context += f"- Name: {full_name}\n"
        if self.context.email:
            context += f"- Email: {self.context.email}\n"
        if self.context.phone:
            context += f"- Phone: {self.context.phone}\n"
        if self.context.gender:
            context += f"- Gender: {self.context.gender}\n"
        if self.context.birth_date:
            context += f"- Date of Birth: {self.context.birth_date}\n"
        if self.context.reference_id:
            context += f"- Reference ID: {self.context.reference_id}\n"
        
        # Address and location
        context += "\n### Address Information:\n"
        # Always include these fields even if empty, to make it clear to the LLM what's available
        context += f"- Address: {self.context.address or 'Not provided'}\n"
        context += f"- City: {self.context.city or 'Not provided'}\n"
        context += f"- State: {self.context.state or 'Not provided'}\n"
        context += f"- Country: {self.context.country or 'Not provided'}\n"
        
        # Only include these if they have values
        if self.context.building:
            context += f"- Building: {self.context.building}\n"
        if self.context.street:
            context += f"- Street: {self.context.street}\n"
        if self.context.zip_code:
            context += f"- Zip Code: {self.context.zip_code}\n"
            
        # SACCO information
        context += f"- SACCO Name: {self.context.sacco_name or 'Not provided'}\n"
        
        # ID information
        context += "\n### Identification Information:\n"
        context += f"- ID Available: {self.context.identification_available}\n"
        context += f"- National ID: {self.context.national_id or 'Not provided'}\n"
        
        if self.context.kcb_account:
            context += f"- KCB Account: {self.context.kcb_account}\n"
        if self.context.kra_pin:
            context += f"- KRA Pin: {self.context.kra_pin}\n"
        
        # Preferences and selections
        context += "\n### Preferences and Selections:\n"
        context += f"- Selected Bike Model: {self.context.selected_model or 'Not selected'}\n"
        context += f"- Selected Showroom: {self.context.selected_showroom or 'Not selected'}\n"
        context += f"- Scheduled Appointment: {self.context.scheduled_time or 'Not scheduled'}\n"
        
        if self.context.model_price:
            context += f"- Model Price: KES {self.context.model_price:,.2f}\n"
        if self.context.compared_models:
            context += f"- Compared Models: {', '.join(self.context.compared_models)}\n"
        if self.context.verbal_language:
            context += f"- Verbal Language: {self.context.verbal_language}\n"
        if self.context.sms_language:
            context += f"- SMS Language: {self.context.sms_language}\n"
        
        # User interest and status information
        context += "\n### Status Information:\n"
        context += f"- Interested in Purchase: {'Yes' if self.context.is_interested else 'No' if self.context.is_interested is not None else 'Unknown'}\n"
        context += f"- Has Required Documents: {'Yes' if self.context.has_documents else 'No' if self.context.has_documents is not None else 'Unknown'}\n"
        context += f"- Interested in EMI: {'Yes' if self.context.wants_emi else 'No' if self.context.wants_emi is not None else 'Unknown'}\n"
        context += f"- Data Sharing Consent: {'Yes' if self.context.data_sharing_consent else 'No'}\n"
        
        # Add preference details if any exist
        if self.context.preferences:
            context += "\n### Detailed Preferences:\n"
            for key, value in self.context.preferences.items():
                context += f"- {key}: {value}\n"
        
        # Image upload status - Make this section more prominent
        context += "\n### DOCUMENT UPLOAD STATUS (IMPORTANT FOR DECISIONS):\n"
        context += f"- ID Uploaded: {'Yes' if self.context.image_uploaded else 'No'}\n"
        context += f"- ID Upload Complete: {'Yes' if self.context.id_upload_complete else 'No'}\n"
        context += f"- Upload Attempts: {self.context.image_upload_attempts}\n"
        context += f"- Has Extracted Information: {'Yes' if self.context.extracted_info else 'No'}\n"
        
        # Add a reminder about checking these states for upload_national_id node
        if self.context.current_node_id in ["upload_national_id", "process_national_id"]:
            context += "\nIMPORTANT: For the upload_national_id and process_national_id nodes, do NOT proceed if the above 'ID Uploaded' and 'ID Upload Complete' states are not 'Yes'. The user must actually upload their ID document rather than just claiming they have.\n"
            
        # Additional document extraction information
        if self.context.extracted_from_image:
            context += "\n### Information Extracted from ID:\n"
            for key, value in self.context.extracted_from_image.items():
                if value:  # Only include non-empty values
                    context += f"- {key}: {value}\n"
        
        return context
    
    def _prepare_conversation_history(self) -> str:
        """Prepare recent conversation history for the prompt"""
        # Get more conversation history (increased from 5 to 10 messages)
        recent_history = self.context.get_recent_history(10)
        
        if not recent_history:
            return "No previous conversation."
            
        # Format into a readable string with clear demarcation between messages
        history_text = "## RECENT CONVERSATION HISTORY (Most recent at bottom):\n\n"
        
        # Add node context
        current_node_id = self.context.current_node_id
        history_text += f"Current node: {current_node_id}\n\n"
        
        for i, msg in enumerate(recent_history):
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "user":
                history_text += f"User: {content}\n\n"
            elif role == "assistant":
                history_text += f"Assistant: {content}\n\n"
            elif role == "system":
                # Include system messages with special formatting for better context
                history_text += f"[System note: {content}]\n\n"
                
        # Add explicit instructions for the LLM
        history_text += "\nIMPORTANT: Review this conversation history carefully before responding. Pay special attention to the user's most recent responses and how they relate to previous questions. Don't confuse responses to one question with another.\n"
        
        return history_text

# Usage example
if __name__ == "__main__":
    pipeline = SalePipeline()
    
    # Start conversation
    response = pipeline.start_conversation()
    print(f"Assistant: {response['message']}")
    
    # Main conversation loop
    while response.get("next_node") != "None":
        user_input = input("User: ")
        if user_input.lower() in ["exit", "quit", "bye"]:
            break
        
        # Example of handling an image upload (this would be in chatbot.py)
        if user_input.lower() == "upload":
            # This is a simplified example - in chatbot.py you would get the image data from the UI
            print("Processing image upload...")
            # Assume this is base64 encoded image data
            sample_image_data = "base64_encoded_image_data_here"
            upload_result = pipeline.process_image_upload(sample_image_data)
            print(f"Upload result: {upload_result['message']}")
            continue
            
        response = pipeline.continue_conversation(user_input)
        print(f"Assistant: {response['message']}")
        print(f"[DEBUG] Current: {response['current_node']} → Next: {response['next_node']}")
