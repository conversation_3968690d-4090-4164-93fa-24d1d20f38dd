import logging
import json
from typing import Dict, Any, List, Optional, Tuple, Callable
import os
from google import genai
from google.genai import types

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Initialize Gemini client
try:
    client = genai.Client(api_key=os.getenv("GEMINI_API_KEY"))
except Exception as e:
    logger.error(f"Error initializing Gemini client: {str(e)}")
    client = None

# Function declarations for each agent
initial_message_function = {
    "name": "create_initial_message",
    "description": "Creates a welcoming first message for potential franchisees",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": []
    }
}

collect_business_type_function = {
    "name": "collect_business_type",
    "description": "Collects information about the user's preferred business type",
    "parameters": {
        "type": "object",
        "properties": {
            "business_type": {
                "type": "string",
                "description": "The type of business the user is interested in (e.g., restaurant, retail, service)"
            }
        },
        "required": ["business_type"]
    }
}

collect_investment_capacity_function = {
    "name": "collect_investment_capacity",
    "description": "Collects information about the user's investment capacity",
    "parameters": {
        "type": "object",
        "properties": {
            "investment_capacity": {
                "type": "number",
                "description": "The amount of money the user is willing to invest in the franchise"
            }
        },
        "required": ["investment_capacity"]
    }
}

collect_location_function = {
    "name": "collect_location",
    "description": "Collects information about the user's preferred location",
    "parameters": {
        "type": "object",
        "properties": {
            "location": {
                "type": "string",
                "description": "The location where the user wants to open the franchise"
            }
        },
        "required": ["location"]
    }
}

collect_experience_function = {
    "name": "collect_experience",
    "description": "Collects information about the user's prior business experience",
    "parameters": {
        "type": "object",
        "properties": {
            "experience": {
                "type": "string",
                "description": "The user's prior business experience"
            }
        },
        "required": ["experience"]
    }
}

score_lead_function = {
    "name": "score_lead",
    "description": "Scores the lead based on collected information",
    "parameters": {
        "type": "object",
        "properties": {
            "business_type": {
                "type": "string",
                "description": "The type of business the user is interested in"
            },
            "investment_capacity": {
                "type": "number",
                "description": "The amount of money the user is willing to invest"
            },
            "location": {
                "type": "string",
                "description": "The user's preferred location"
            },
            "experience": {
                "type": "string",
                "description": "The user's prior business experience"
            }
        },
        "required": ["business_type", "investment_capacity"]
    }
}

provide_business_model_info_function = {
    "name": "provide_business_model_info",
    "description": "Provides information about franchise business models",
    "parameters": {
        "type": "object",
        "properties": {
            "business_type": {
                "type": "string",
                "description": "The type of business to provide information about"
            }
        },
        "required": ["business_type"]
    }
}

provide_profit_margin_info_function = {
    "name": "provide_profit_margin_info",
    "description": "Provides information about profit margins for different franchise types",
    "parameters": {
        "type": "object",
        "properties": {
            "business_type": {
                "type": "string",
                "description": "The type of business to provide profit margin information about"
            }
        },
        "required": ["business_type"]
    }
}

# Function handlers
def create_initial_message():
    """Creates a welcoming first message for potential franchisees"""
    message = (
        "Welcome to the Franchise Agent! I'm here to help you explore franchise opportunities. "
        "I'll ask you some questions to understand your business interests and investment capacity."
    )

    # Return message and state updates
    return {
        "message": message,
        "state_updates": [
            {"key": "current_agent", "value": "RequirementsCollectionAgent", "action": "update"},
            {"key": "conversation_started", "value": True, "action": "update"}
        ]
    }

def collect_business_type(business_type):
    """Collects information about the user's preferred business type"""
    message = f"Thank you for sharing that you're interested in {business_type}. This is a popular franchise category."

    # Return message and state updates
    return {
        "message": message,
        "state_updates": [
            {"key": "business_type", "value": business_type, "action": "update"}
        ]
    }

def collect_investment_capacity(investment_capacity):
    """Collects information about the user's investment capacity"""
    message = f"I see that you have an investment capacity of ${investment_capacity}. This will help us find suitable franchise opportunities for you."

    # Return message and state updates
    return {
        "message": message,
        "state_updates": [
            {"key": "investment_capacity", "value": investment_capacity, "action": "update"}
        ]
    }

def collect_location(location):
    """Collects information about the user's preferred location"""
    message = f"Thank you for letting me know that you're interested in opening a franchise in {location}."

    # Return message and state updates
    return {
        "message": message,
        "state_updates": [
            {"key": "location", "value": location, "action": "update"}
        ]
    }

def collect_experience(experience):
    """Collects information about the user's prior business experience"""
    message = f"I understand that you have {experience} in terms of business experience. This will be helpful in finding the right franchise opportunity for you."

    # Return message and state updates
    return {
        "message": message,
        "state_updates": [
            {"key": "experience", "value": experience, "action": "update"}
        ]
    }

def score_lead(business_type, investment_capacity, location=None, experience=None):
    """
    This function is now a pass-through that skips lead scoring and transitions directly to lead nurturing.
    We keep it for backward compatibility but it no longer performs actual scoring.
    """
    # Set default values without scoring
    score = 50  # Default middle score
    lead_quality = "Warm"  # Default middle quality

    message = (
        f"Thank you for providing your information about your {business_type} business interest. "
        f"I'd be happy to provide more information about franchise opportunities that match your profile. "
        f"What specific aspects of our franchise opportunities would you like to know more about?"
    )

    # Return message and state updates - transition directly to LeadNurturingAgent
    return {
        "message": message,
        "state_updates": [
            {"key": "lead_score", "value": score, "action": "update"},
            {"key": "lead_quality", "value": lead_quality, "action": "update"},
            {"key": "current_agent", "value": "LeadNurturingAgent", "action": "update"}
        ]
    }

def provide_business_model_info(business_type):
    """Provides information about franchise business models using LLM"""
    try:
        # Try to use the LLM to generate a response
        if client:
            prompt = f"Provide a SHORT (2-3 sentences) explanation of business models for {business_type} franchises in the EV industry. Be conversational and friendly."

            # Use the chat completions API which is more reliable
            response = client.chat.completions.create(
                model="gemini-2.0-flash",
                messages=[
                    {"role": "system", "content": "You are a franchise expert. Keep responses short and conversational."},
                    {"role": "user", "content": prompt}
                ]
            )

            if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                return {
                    "message": response.choices[0].message.content,
                    "state_updates": []
                }
    except Exception as e:
        logger.error(f"Error generating business model info with LLM: {str(e)}")

    # If LLM fails or is not available, use a generic dynamic response
    message = f"EV franchises for {business_type} businesses typically involve multiple revenue streams. Would you like to know more about the specific business model options?"

    # Return message and state updates
    return {
        "message": message,
        "state_updates": []
    }

def provide_profit_margin_info(business_type):
    """Provides information about profit margins for different franchise types using LLM"""
    try:
        # Try to use the LLM to generate a response
        if client:
            prompt = f"Provide a SHORT (2-3 sentences) explanation of profit margins for {business_type} franchises in the EV industry. Be conversational and friendly."

            # Use the chat completions API which is more reliable
            response = client.chat.completions.create(
                model="gemini-2.0-flash",
                messages=[
                    {"role": "system", "content": "You are a franchise expert. Keep responses short and conversational."},
                    {"role": "user", "content": prompt}
                ]
            )

            if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                return {
                    "message": response.choices[0].message.content,
                    "state_updates": []
                }
    except Exception as e:
        logger.error(f"Error generating profit margin info with LLM: {str(e)}")

    # If LLM fails or is not available, use a generic dynamic response
    message = f"Profit margins for EV franchises in the {business_type} sector can vary based on location and business model. Would you like to know more about specific factors that affect profitability?"

    # Return message and state updates
    return {
        "message": message,
        "state_updates": []
    }

# Add a function to handle general franchise questions
def answer_franchise_question(question_type, answer=None):
    """Answers general questions about franchising using LLM"""
    try:
        # Try to use the LLM to generate a response
        if client:
            prompt = f"Answer this question about EV franchises: {question_type}. Keep your response SHORT (2-3 sentences), conversational, and friendly."

            # Use the chat completions API which is more reliable
            response = client.chat.completions.create(
                model="gemini-2.0-flash",
                messages=[
                    {"role": "system", "content": "You are a franchise expert. Keep responses short and conversational."},
                    {"role": "user", "content": prompt}
                ]
            )

            if hasattr(response, 'choices') and len(response.choices) > 0 and hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                return {
                    "message": response.choices[0].message.content,
                    "state_updates": []
                }
    except Exception as e:
        logger.error(f"Error generating franchise question answer with LLM: {str(e)}")

    # If LLM fails or is not available, use a generic dynamic response based on the question type
    message = f"That's a great question about {question_type}. Would you like me to provide more specific information about this aspect of EV franchises?"

    # Return message and state updates
    return {
        "message": message,
        "state_updates": []
    }

# Function handler for the final agent
def provide_final_message(message=None):
    """Provides a final message to the user informing them that a human agent will contact them"""
    if not message:
        message = (
            "Thank you for your interest in Spiro's franchise opportunities! "
            "A human agent will contact you soon to discuss the next steps. "
            "If you have any urgent questions, please contact us at:\n\n"
            "Phone: 254 116 931615\n"
            "Website: https://www.spironet.com"
        )

    # Return message and state updates
    return {
        "message": message,
        "state_updates": []
    }

# Map function names to handlers
function_handlers = {
    "create_initial_message": create_initial_message,
    "collect_business_type": collect_business_type,
    "collect_investment_capacity": collect_investment_capacity,
    "collect_location": collect_location,
    "collect_experience": collect_experience,
    "score_lead": score_lead,
    "provide_business_model_info": provide_business_model_info,
    "provide_profit_margin_info": provide_profit_margin_info,
    "answer_franchise_question": answer_franchise_question,
    "provide_final_message": provide_final_message
}

# Function declarations for each agent
initial_message_functions = [initial_message_function]
requirements_collection_functions = [
    collect_business_type_function,
    collect_investment_capacity_function,
    collect_location_function,
    collect_experience_function
]
lead_scoring_functions = [score_lead_function]
lead_nurturing_functions = [
    provide_business_model_info_function,
    provide_profit_margin_info_function,
    {
        "name": "answer_franchise_question",
        "description": "Answers general questions about franchising",
        "parameters": {
            "type": "object",
            "properties": {
                "question_type": {
                    "type": "string",
                    "description": "The type of question being asked"
                },
                "answer": {
                    "type": "string",
                    "description": "The answer to the question"
                }
            },
            "required": ["question_type"]
        }
    }
]

# Define function for the final agent
final_agent_function = {
    "name": "provide_final_message",
    "description": "Provides a final message to the user informing them that a human agent will contact them",
    "parameters": {
        "type": "object",
        "properties": {
            "message": {
                "type": "string",
                "description": "The final message to send to the user"
            }
        },
        "required": ["message"]
    }
}

# Create a list of functions for the final agent
final_agent_functions = [final_agent_function]

class FunctionCallingService:
    """Service for handling function calling with Gemini"""

    @staticmethod
    def get_functions_for_agent(agent_name: str) -> List[Dict[str, Any]]:
        """Get the appropriate functions for the current agent"""
        if agent_name == "InitialMessageAgent":
            return initial_message_functions
        elif agent_name == "RequirementsCollectionAgent":
            return requirements_collection_functions
        elif agent_name == "LeadScoringAgent":
            return lead_scoring_functions
        elif agent_name == "LeadNurturingAgent":
            return lead_nurturing_functions
        elif agent_name == "FinalAgent":
            return final_agent_functions
        else:
            return []

    @staticmethod
    async def process_message(user_input: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process a user message using function calling"""
        if not client:
            logger.error("Gemini client not initialized")
            return {
                "message": "Sorry, I'm having trouble connecting to the AI service. Please try again later.",
                "state_updates": []
            }

        # Get the current agent
        current_agent = state.get("current_agent", "InitialMessageAgent")

        # Get functions for current agent
        functions = FunctionCallingService.get_functions_for_agent(current_agent)

        try:
            # Configure the client with functions
            tools = types.Tool(function_declarations=functions)
            config = types.GenerateContentConfig(tools=[tools])

            # Create conversation history
            messages = state.get("messages", [])

            # Add user message to history
            messages.append({"role": "user", "content": user_input})

            # Send request to Gemini
            response = client.models.generate_content(
                model="gemini-2.0-flash",
                contents=user_input,
                config=config
            )

            # Check for function call
            if response.candidates[0].content.parts[0].function_call:
                function_call = response.candidates[0].content.parts[0].function_call
                function_name = function_call.name
                function_args = function_call.args

                # Call the appropriate function handler
                handler = function_handlers.get(function_name)
                if handler:
                    result = handler(**function_args)

                    # Add assistant message to history
                    messages.append({"role": "assistant", "content": result.get("message", "")})

                    # Update state with messages
                    state["messages"] = messages

                    return result
                else:
                    logger.error(f"Function handler not found for {function_name}")
                    return {
                        "message": "I'm sorry, I encountered an error processing your request.",
                        "state_updates": []
                    }
            else:
                # No function call, use the direct response
                message = response.text

                # Add assistant message to history
                messages.append({"role": "assistant", "content": message})

                # Update state with messages
                state["messages"] = messages

                return {
                    "message": message,
                    "state_updates": []
                }
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return {
                "message": "I'm sorry, I encountered an error processing your request.",
                "state_updates": []
            }
